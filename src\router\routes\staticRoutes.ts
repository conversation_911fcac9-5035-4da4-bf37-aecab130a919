import { RouteItem } from '@/types/route'
const Layout = () => import('@/layout/index.vue')

// 公共路由,配置详情请参见RouteItem定义
export const constantRoutes: RouteItem[] = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/auth.vue'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/auth.vue'),
    hidden: true
  },
  {
    path: '/tool/matrix-mix/video-edit/:projectId',
    component: () => import('@/views/tool/MatrixMix/videoEdit/index.vue'),
    hidden: true,
    meta: { title: '视频编辑' }
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404.vue'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/project',
    component: Layout,
    meta: { isTopMenu: true },
    children: [
      {
        path: '',
        component: () => import('@/views/platform/project/index.vue'),
        name: 'project',
        meta: { title: '数智宝直播平台', icon: 'excel', affix: true }
      },
      {
        path: ':projectId',
        component: () => import('@/views/platform/project/detail.vue'),
        hidden: true,
        alwaysShow: true,
        meta: { title: '项目列表', icon: 'excel' },
        redirect: '/project',
        children: [
          {
            path: 'material',
            component: () => import('@/views/platform/material/index.vue'),
            name: '文案',
            hidden: true,
            alwaysShow: true,
            meta: { title: (route: any) => route.query?.projectTitle as string || '文案', icon: 'excel', group: (route: any) => route.params?.projectId as string || '', transition: 'none' }
          },
          {
            path: 'live',
            component: () => import('@/views/platform/live/index.vue'),
            name: '规则',
            hidden: true,
            alwaysShow: true,
            meta: { title: (route: any) => route.query?.projectTitle as string || '规则', icon: 'excel', group: (route: any) => route.params?.projectId as string || '', transition: 'none' }
          },
          {
            path: 'broadcast',
            component: () => import('@/views/platform/broadcast/index.vue'),
            name: '直播',
            hidden: true,
            alwaysShow: true,
            meta: { title: (route: any) => route.query?.projectTitle as string || '直播', icon: 'excel', group: (route: any) => route.params?.projectId as string || '', transition: 'none' }
          },
          {
            path: 'anchor',
            component: () => import('@/views/platform/anchor/index.vue'),
            name: '智能主播',
            hidden: true,
            alwaysShow: true,
            meta: { title: (route: any) => route.query?.projectTitle as string || '智能主播', icon: 'excel', group: (route: any) => route.params?.projectId as string || '', transition: 'none' }
          }
        ]
      },
    ]
  },
  {
    path: '/record',
    component: Layout,
    meta: { isTopMenu: true },
    children: [
      {
        path: '',
        component: () => import('@/views/platform/record/index.vue'),
        name: 'record',
        meta: { title: '数智宝音频转文本', icon: 'eye-open', affix: true }
      },
      {
        path: 'detail/:vaId',
        component: () => import('@/views/platform/record/detail.vue'),
        name: 'recordDetail',
        hidden: true,
        meta: { title: '音频详情', icon: '' }
      },
    ]
  },
  // {
  //   path: '/laboratory',
  //   component: Layout,
  //   meta: { title: '实验室', icon: 'dashboard' },
  //   children: [
  //     {
  //       path: 'threeTest',
  //       component: () => import('@/views/three/gltfmode.vue'),
  //       name: 'threeTest',
  //       meta: { title: 'three实验室', icon: 'dashboard' }
  //     },
  //     {
  //       path: "websocket",
  //       component: () => import('@/views/websocket.vue'),
  //       meta: { title: 'websocket实验室', icon: 'dashboard' }
  //     }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
]