/**
 * Canvas渲染引擎 - 统一管理视频、音频、字幕的渲染和同步
 */

import type { Timeline, TimelineClip } from '../types/videoEdit';
import { getMediaInfo } from '../api/media';
import { batchMediaProcessor } from './batchMediaProcessor';
import { timelineDebugger } from '../videoEdit/debug/templateDataDebug';

interface MediaAsset {
  id: string;
  url: string;
  element: HTMLVideoElement | HTMLAudioElement;
  loaded: boolean;
  duration: number;
}

interface SubtitleStyle {
  fontSize: number;
  fontFamily: string;
  color: string;
  backgroundColor?: string;
  strokeColor?: string;
  strokeWidth?: number;
  alignment: 'left' | 'center' | 'right';
  position: 'top' | 'middle' | 'bottom';
}

export class CanvasRenderEngine {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private timeline: Timeline | null = null;
  
  private videoAssets = new Map<string, MediaAsset>();
  private audioAssets = new Map<string, MediaAsset>();
  
  private currentTime = 0;
  private isPlaying = false;
  private animationFrameId: number | null = null;
  private lastFrameTimestamp = 0;
  
  private audioContext: AudioContext | null = null;
  private masterGainNode: GainNode | null = null;
  private audioSources = new Map<string, AudioBufferSourceNode>();
  
  // 视频音频混合器
  private videoAudioSources = new Map<string, MediaElementAudioSourceNode>();
  private videoGainNodes = new Map<string, GainNode>();
  
  // 音频电平监控
  private audioAnalyser: AnalyserNode | null = null;
  private channelSplitter: ChannelSplitterNode | null = null;
  private leftAnalyser: AnalyserNode | null = null;
  private rightAnalyser: AnalyserNode | null = null;
  private audioLevelData: { left: number; right: number; peak: number } = { left: 0, right: 0, peak: 0 };
  private onAudioLevelUpdate?: (levels: { left: number; right: number; peak: number }) => void;
  private lastLogTime: number = 0;
  
  private onTimeUpdate?: (time: number) => void;
  private onPlayStateChange?: (isPlaying: boolean) => void;
  private onDurationChange?: (duration: number) => void;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.initializeAudioContext();
  }

  private async initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.masterGainNode = this.audioContext.createGain();
      
      // 创建音频分析器用于电平监控
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = 2048;
      this.audioAnalyser.smoothingTimeConstant = 0.3;
      
      // 创建声道分离器用于左右声道分析
      this.channelSplitter = this.audioContext.createChannelSplitter(2);
      
      // 创建左右声道分析器
      this.leftAnalyser = this.audioContext.createAnalyser();
      this.leftAnalyser.fftSize = 2048;
      this.leftAnalyser.smoothingTimeConstant = 0.3;
      
      this.rightAnalyser = this.audioContext.createAnalyser();
      this.rightAnalyser.fftSize = 2048;
      this.rightAnalyser.smoothingTimeConstant = 0.3;
      
      // 建立音频链路：masterGainNode -> [audioAnalyser, channelSplitter] -> destination
      this.masterGainNode.connect(this.audioAnalyser);
      this.masterGainNode.connect(this.channelSplitter);
      
      // 连接左右声道到对应的分析器
      this.channelSplitter.connect(this.leftAnalyser, 0);  // 左声道
      this.channelSplitter.connect(this.rightAnalyser, 1); // 右声道
      
      // 主输出
      this.audioAnalyser.connect(this.audioContext.destination);
      
      // 启动音频电平监控
      this.startAudioLevelMonitoring();
    } catch (error) {
      console.error('Web Audio API 初始化失败:', error);
    }
  }

  private updateAudioPlayback(currentTime: number) {
    if (!this.timeline || !this.audioContext) return;

    // 处理时间轴音频轨道（独立音频文件）
    if (this.timeline.AudioTracks) {
      this.timeline.AudioTracks.forEach(track => {
        const audioClips = track.AudioTrackClips;
        
        audioClips.forEach(clip => {
          const isActive = currentTime >= clip.TimelineIn && currentTime < clip.TimelineOut;
          const sourceKey = `${clip.MediaId}_${clip.TimelineIn}_${track.Id || 0}`;
          
          if (isActive && !this.audioSources.has(sourceKey)) {
            if (this.isPlaying) {
              this.startAudioClip(clip, currentTime, sourceKey);
            }
          } else if (!isActive && this.audioSources.has(sourceKey)) {
            this.stopAudioClip(sourceKey);
          } else if (isActive && this.audioSources.has(sourceKey)) {
            if (!this.isPlaying) {
              this.stopAudioClip(sourceKey);
            }
          }
        });
      });
    }

    // 处理视频轨道中的音频（视频文件的音频流）
    if (this.timeline.VideoTracks) {
      this.timeline.VideoTracks.forEach(track => {
        track.VideoTrackClips.forEach(clip => {
          const isActive = currentTime >= clip.TimelineIn && currentTime < clip.TimelineOut;
          const gainNode = this.videoGainNodes.get(clip.MediaId);
          
          if (gainNode) {
            // 根据播放状态调整视频音频音量
            if (isActive && this.isPlaying) {
              gainNode.gain.value = 1.0; // 启用视频音频
            } else {
              gainNode.gain.value = 0.0; // 静音视频音频
            }
          }
        });
      });
    }

    // 处理 HTML Audio 元素同步 - 需要检查所有轨道
    this.audioAssets.forEach((asset, mediaId) => {
      if (asset.element instanceof HTMLAudioElement) {
        const audio = asset.element;
        let activeClip = null;
        
        // 查找当前活跃的音频片段
        if (this.timeline?.AudioTracks) {
          for (const track of this.timeline.AudioTracks) {
            activeClip = track.AudioTrackClips.find(clip => 
              clip.MediaId === mediaId && 
              currentTime >= clip.TimelineIn && 
              currentTime < clip.TimelineOut
            );
            if (activeClip) break;
          }
        }

        if (activeClip) {
          const internalTime = (currentTime - activeClip.TimelineIn) + activeClip.In;
          
          if (Math.abs(audio.currentTime - internalTime) > 0.2) {
            audio.currentTime = internalTime;
          }
          
          if (this.isPlaying && audio.paused) {
            audio.play().catch(() => {});
          } else if (!this.isPlaying && !audio.paused) {
            audio.pause();
          }
        } else {
          if (!audio.paused) {
            audio.pause();
          }
        }
      }
    });
  }

  async setTimeline(timeline: Timeline | null) {
    this.stopAllAudio();
    this.timeline = timeline;
    
    if (timeline) {
      // 使用批量媒体处理器优化性能
      const processingResult = await batchMediaProcessor.processTimelineMedia(timeline);
      
      if (processingResult.success && processingResult.timeline) {
        this.timeline = processingResult.timeline;
      } else {
        await this.resolveMediaUrls();
      }
      
      await this.loadMediaAssets();
      this.calculateTotalDuration();
      
      // 媒体资源加载完成后，立即渲染当前时间点的帧
      this.renderFrame(this.currentTime);
    } else {
      this.clearAssets();
    }
  }

  /**
   * 解析MediaId获取对应的FileUrl
   */
  private async resolveMediaUrls() {
    if (!this.timeline) return;

    const mediaIds = new Set<string>();
    
    // 收集需要解析URL的MediaId
    this.timeline.VideoTracks?.forEach(track => {
      track.VideoTrackClips.forEach(clip => {
        if (clip.MediaId && !clip.FileUrl) {
          mediaIds.add(clip.MediaId);
        }
      });
    });

    this.timeline.AudioTracks?.forEach(track => {
      track.AudioTrackClips.forEach(clip => {
        if (clip.MediaId && !clip.FileUrl) {
          mediaIds.add(clip.MediaId);
        }
      });
    });

    if (mediaIds.size === 0) {
      return;
    }

    // 批量获取媒体信息
    const mediaInfoPromises = Array.from(mediaIds).map(async (mediaId) => {
      try {
        const response = await getMediaInfo({ mediaId });
        
        if (response?.MediaInfo?.FileInfoList && response.MediaInfo.FileInfoList.length > 0) {
          const fileUrl = response.MediaInfo.FileInfoList[0].FileBasicInfo.FileUrl;
          return { mediaId, fileUrl };
        } else {
          return { mediaId, fileUrl: null };
        }
      } catch (error) {
        return { mediaId, fileUrl: null };
      }
    });

    try {
      const results = await Promise.all(mediaInfoPromises);
      const urlMap = new Map<string, string>();

      results.forEach(({ mediaId, fileUrl }) => {
        if (fileUrl) {
          urlMap.set(mediaId, fileUrl);
        }
      });

      // 更新Timeline中的FileUrl
      this.timeline.VideoTracks?.forEach(track => {
        track.VideoTrackClips.forEach(clip => {
          if (clip.MediaId && !clip.FileUrl && urlMap.has(clip.MediaId)) {
            clip.FileUrl = urlMap.get(clip.MediaId)!;
          }
        });
      });

      this.timeline.AudioTracks?.forEach(track => {
        track.AudioTrackClips.forEach(clip => {
          if (clip.MediaId && !clip.FileUrl && urlMap.has(clip.MediaId)) {
            clip.FileUrl = urlMap.get(clip.MediaId)!;
          }
        });
      });

    } catch (error) {
      console.error('URL解析失败:', error);
    }
  }

  private async loadMediaAssets() {
    if (!this.timeline) return;

    const loadPromises: Promise<void>[] = [];

    // 加载视频轨道媒体资源
    if (this.timeline.VideoTracks) {
      this.timeline.VideoTracks.forEach((track) => {
        track.VideoTrackClips.forEach((clip) => {
          if (clip.FileUrl && !this.videoAssets.has(clip.MediaId)) {
            loadPromises.push(this.loadVideoAsset(clip.MediaId, clip.FileUrl));
          }
        });
      });
    }

    // 加载音频轨道媒体资源
    if (this.timeline.AudioTracks) {
      this.timeline.AudioTracks.forEach((track) => {
        track.AudioTrackClips.forEach((clip) => {
          if (clip.FileUrl && !this.audioAssets.has(clip.MediaId)) {
            loadPromises.push(this.loadAudioAsset(clip.MediaId, clip.FileUrl));
          }
        });
      });
    }

    if (loadPromises.length === 0) {
      return;
    }

    try {
      await Promise.all(loadPromises);
    } catch (error) {
      console.error('媒体资源加载失败:', error);
    }
  }

  private convertToProxyUrl(originalUrl: string): string {
    if (originalUrl.includes('szb-pc.oss-cn-beijing.aliyuncs.com')) {
      if (import.meta.env.DEV) {
        const urlParts = originalUrl.split('szb-pc.oss-cn-beijing.aliyuncs.com');
        if (urlParts.length === 2) {
          return `/media-proxy${urlParts[1]}`;
        }
      }
    }
    return originalUrl;
  }

  private loadVideoAsset(mediaId: string, url: string): Promise<void> {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const proxyUrl = this.convertToProxyUrl(url);
      
      if (proxyUrl === url) {
        video.crossOrigin = 'anonymous';
      }
      
      video.preload = 'metadata';
      
      const asset: MediaAsset = {
        id: mediaId,
        url: proxyUrl,
        element: video,
        loaded: false,
        duration: 0
      };

      let loadTimeout: number;

      const onLoadSuccess = () => {
        clearTimeout(loadTimeout);
        asset.loaded = true;
        asset.duration = video.duration || 0;
        this.videoAssets.set(mediaId, asset);
        // 设置视频音频源到Web Audio API进行混合
        this.setupVideoAudio(mediaId, video);
        resolve();
      };

      const onLoadError = (event: any) => {
        clearTimeout(loadTimeout);
        
        if (proxyUrl !== url && !video.crossOrigin) {
          video.crossOrigin = 'anonymous';
          video.src = url;
          return;
        }
        
        resolve(); // 不阻塞其他资源加载
      };

      const onTimeout = () => {
        onLoadError({ type: 'timeout' });
      };

      loadTimeout = setTimeout(onTimeout, 10000);

      video.onloadedmetadata = onLoadSuccess;
      video.oncanplay = onLoadSuccess;
      video.onerror = onLoadError;

      video.src = proxyUrl;
      video.load();
    });
  }

  /**
   * 设置视频音频源，将视频音频连接到Web Audio API进行混合
   */
  private setupVideoAudio(mediaId: string, video: HTMLVideoElement) {
    if (!this.audioContext || !this.masterGainNode) return;
    
    try {
      // 检查是否已经设置过，避免重复创建
      if (this.videoAudioSources.has(mediaId)) return;
      
      // 创建音频源节点
      const audioSource = this.audioContext.createMediaElementSource(video);
      
      // 创建增益节点来控制视频音频音量
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = 1.0; // 默认音量
      
      // 连接音频路径：视频 -> 增益 -> 主输出
      audioSource.connect(gainNode);
      gainNode.connect(this.masterGainNode);
      
      // 保存引用
      this.videoAudioSources.set(mediaId, audioSource);
      this.videoGainNodes.set(mediaId, gainNode);
    } catch (error) {
      console.error('设置视频音频源失败:', error);
    }
  }

  private async loadAudioAsset(mediaId: string, url: string): Promise<void> {
    if (!this.audioContext) return;

    const proxyUrl = this.convertToProxyUrl(url);

    try {
      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: { 'Accept': 'audio/*,*/*;q=0.9' }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status} - ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      const asset: MediaAsset = {
        id: mediaId,
        url: proxyUrl,
        element: new Audio(),
        loaded: true,
        duration: audioBuffer.duration
      };

      (asset as any).audioBuffer = audioBuffer;
      this.audioAssets.set(mediaId, asset);
      
    } catch (error) {
      // 降级到 HTML Audio
      try {
        const audioElement = document.createElement('audio');
        audioElement.preload = 'auto';
        audioElement.src = proxyUrl;
        
        const asset: MediaAsset = {
          id: mediaId,
          url: proxyUrl,
          element: audioElement,
          loaded: true,
          duration: 0
        };
        
        this.audioAssets.set(mediaId, asset);
      } catch (fallbackError) {
        // 静默失败
      }
    }
  }

  private calculateTotalDuration() {
    if (!this.timeline) return;

    let maxDuration = 0;

    // 计算所有视频轨道的最大时长
    this.timeline.VideoTracks?.forEach(track => {
      track.VideoTrackClips.forEach(clip => {
        maxDuration = Math.max(maxDuration, clip.TimelineOut);
      });
    });

    // 计算所有音频轨道的最大时长
    this.timeline.AudioTracks?.forEach(track => {
      track.AudioTrackClips.forEach(clip => {
        maxDuration = Math.max(maxDuration, clip.TimelineOut);
      });
    });

    // 计算所有字幕轨道的最大时长
    this.timeline.SubtitleTracks?.forEach(track => {
      track.SubtitleTrackClips.forEach(clip => {
        maxDuration = Math.max(maxDuration, clip.TimelineOut);
      });
    });

    this.onDurationChange?.(maxDuration);
  }

  private renderLoop = (timestamp: number) => {
    if (!this.isPlaying) return;

    if (this.lastFrameTimestamp !== 0) {
      const deltaTime = (timestamp - this.lastFrameTimestamp) / 1000;
      this.currentTime += deltaTime;
      this.onTimeUpdate?.(this.currentTime);
    }
    this.lastFrameTimestamp = timestamp;

    this.renderFrame(this.currentTime);
    this.animationFrameId = requestAnimationFrame(this.renderLoop);
  };

  private renderFrame(currentTime: number) {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    if (!this.timeline) return;

    this.renderVideoTracks(currentTime);
    this.renderSubtitleTracks(currentTime);
    this.updateAudioPlayback(currentTime);
  }

  private renderVideoTracks(currentTime: number) {
    if (!this.timeline?.VideoTracks) return;

    // 只处理 Type 为 'Video' 的轨道，排除 'Subtitle' 类型
    const videoOnlyTracks = this.timeline.VideoTracks.filter(track => 
      track.Type === 'Video' || !track.Type // 兼容没有Type字段的情况
    );

    // 按Id降序排序，让Id小的轨道在最上层（最后渲染）
    const sortedTracks = [...videoOnlyTracks].sort((a, b) => (b.Id || 0) - (a.Id || 0));
    
    sortedTracks.forEach((track) => {
      const videoClips = track.VideoTrackClips;
      videoClips.forEach((clip) => {
        if (currentTime >= clip.TimelineIn && currentTime < clip.TimelineOut) {
          this.renderVideoClip(clip, currentTime);
        }
      });
    });
  }

  private renderVideoClip(clip: TimelineClip, currentTime: number) {
    const asset = this.videoAssets.get(clip.MediaId);
    if (!asset || !asset.loaded) {
      this.drawVideoPlaceholder(clip);
      return;
    }

    const video = asset.element as HTMLVideoElement;
    const internalTime = (currentTime - clip.TimelineIn) + clip.In;
    
    if (Math.abs(video.currentTime - internalTime) > 0.2) {
      video.currentTime = internalTime;
    }

    if (video.paused && this.isPlaying) {
      video.play().catch(() => {});
    }

    try {
      this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
    } catch (error) {
      this.drawVideoPlaceholder(clip);
    }
  }

  private drawVideoPlaceholder(clip: TimelineClip) {
    this.ctx.fillStyle = '#2a2a2a';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    const text = `视频加载中...\n${clip.FileName || clip.MediaId}`;
    const lines = text.split('\n');
    const lineHeight = 30;
    const startY = (this.canvas.height / 2) - ((lines.length - 1) * lineHeight / 2);
    
    lines.forEach((line, index) => {
      this.ctx.fillText(line, this.canvas.width / 2, startY + index * lineHeight);
    });
  }

  private renderSubtitleTracks(currentTime: number) {
    if (!this.timeline?.SubtitleTracks) return;

    // 按Id降序排序，让Id小的轨道在最上层（最后渲染）
    const sortedTracks = [...this.timeline.SubtitleTracks].sort((a, b) => (b.Id || 0) - (a.Id || 0));
    
    sortedTracks.forEach(track => {
      const subtitleClips = track.SubtitleTrackClips;
      subtitleClips.forEach(clip => {
        if (currentTime >= clip.TimelineIn && currentTime < clip.TimelineOut) {
          this.renderSubtitleClip(clip);
        }
      });
    });
  }

  private renderSubtitleClip(clip: TimelineClip) {
    const text = clip.Content || '';
    if (!text) return;

    const style: SubtitleStyle = {
      fontSize: 24,
      fontFamily: 'Arial, sans-serif',
      color: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      strokeColor: '#000000',
      strokeWidth: 1,
      alignment: 'center',
      position: 'bottom'
    };

    this.ctx.font = `${style.fontSize}px ${style.fontFamily}`;
    this.ctx.textAlign = style.alignment;
    this.ctx.textBaseline = 'middle';

    let x = this.canvas.width / 2;
    let y = this.canvas.height - 60;

    if (style.alignment === 'left') x = 20;
    if (style.alignment === 'right') x = this.canvas.width - 20;
    if (style.position === 'top') y = 60;
    if (style.position === 'middle') y = this.canvas.height / 2;

    if (style.backgroundColor) {
      const textMetrics = this.ctx.measureText(text);
      const textWidth = textMetrics.width;
      const textHeight = style.fontSize;
      
      this.ctx.fillStyle = style.backgroundColor;
      this.ctx.fillRect(
        x - textWidth / 2 - 10,
        y - textHeight / 2 - 5,
        textWidth + 20,
        textHeight + 10
      );
    }

    if (style.strokeColor && style.strokeWidth) {
      this.ctx.strokeStyle = style.strokeColor;
      this.ctx.lineWidth = style.strokeWidth;
      this.ctx.strokeText(text, x, y);
    }

    this.ctx.fillStyle = style.color;
    this.ctx.fillText(text, x, y);
  }

  private startAudioClip(clip: TimelineClip, currentTime: number, sourceKey: string) {
    const asset = this.audioAssets.get(clip.MediaId);
    if (!asset || !this.audioContext || !this.masterGainNode) return;

    const audioBuffer = (asset as any).audioBuffer;
    
    if (audioBuffer) {
      try {
        const source = this.audioContext.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(this.masterGainNode);

        const offset = (currentTime - clip.TimelineIn) + clip.In;
        const duration = clip.TimelineOut - clip.TimelineIn;

        source.start(this.audioContext.currentTime, offset, duration);
        this.audioSources.set(sourceKey, source);

        source.onended = () => {
          this.audioSources.delete(sourceKey);
        };
      } catch (error) {
        console.error('Web Audio API 播放失败:', error);
      }
    } else if (asset.element instanceof HTMLAudioElement) {
      const audio = asset.element;
      const internalTime = (currentTime - clip.TimelineIn) + clip.In;
      
      if (Math.abs(audio.currentTime - internalTime) > 0.1) {
        audio.currentTime = internalTime;
      }
      
      if (audio.paused && this.isPlaying) {
        audio.play().catch(() => {});
      }
    }
  }

  private stopAudioClip(sourceKey: string) {
    const source = this.audioSources.get(sourceKey);
    if (source) {
      try {
        source.stop();
      } catch {}
      this.audioSources.delete(sourceKey);
    }
    
    this.audioAssets.forEach(asset => {
      if (asset.element instanceof HTMLAudioElement && !asset.element.paused) {
        asset.element.pause();
      }
    });
  }

  play() {
    if (this.isPlaying) return;

    this.isPlaying = true;
    this.lastFrameTimestamp = 0;
    
    // 恢复音频上下文（如果被挂起）
    if (this.audioContext?.state === 'suspended') {
      this.audioContext.resume();
    }

    this.animationFrameId = requestAnimationFrame(this.renderLoop);
    this.onPlayStateChange?.(true);
  }

  pause() {
    if (!this.isPlaying) return;

    this.isPlaying = false;
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // 暂停所有视频
    this.videoAssets.forEach(asset => {
      const video = asset.element as HTMLVideoElement;
      if (!video.paused) {
        video.pause();
      }
    });

    // 停止所有音频
    this.stopAllAudio();
    this.onPlayStateChange?.(false);
  }

  seek(time: number) {
    this.currentTime = Math.max(0, time);
    
    this.stopAllAudio();
    this.renderFrame(this.currentTime);
    this.onTimeUpdate?.(this.currentTime);

    if (this.isPlaying) {
      this.pause();
    }
  }

  private stopAllAudio() {
    // 停止时间轴音频源
    this.audioSources.forEach((source) => {
      try {
        source.stop();
      } catch {}
    });
    this.audioSources.clear();

    // 静音所有视频音频
    this.videoGainNodes.forEach((gainNode) => {
      gainNode.gain.value = 0.0;
    });

    // 暂停所有HTML Audio元素
    this.audioAssets.forEach((asset) => {
      if (asset.element instanceof HTMLAudioElement && !asset.element.paused) {
        asset.element.pause();
      }
    });
  }

  setEventCallbacks(callbacks: {
    onTimeUpdate?: (time: number) => void;
    onPlayStateChange?: (isPlaying: boolean) => void;
    onDurationChange?: (duration: number) => void;
  }) {
    this.onTimeUpdate = callbacks.onTimeUpdate;
    this.onPlayStateChange = callbacks.onPlayStateChange;
    this.onDurationChange = callbacks.onDurationChange;
  }

  getPlayState() {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      hasTimeline: !!this.timeline
    };
  }

  /**
   * 设置视频音频音量
   * @param volume 音量值 0.0 - 1.0
   */
  setVideoAudioVolume(volume: number) {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.videoGainNodes.forEach((gainNode) => {
      gainNode.gain.value = clampedVolume;
    });
  }

  /**
   * 设置时间轴音频音量
   * @param volume 音量值 0.0 - 1.0
   */
  setTimelineAudioVolume(volume: number) {
    if (this.masterGainNode) {
      const clampedVolume = Math.max(0, Math.min(1, volume));
      this.masterGainNode.gain.value = clampedVolume;
    }
  }

  /**
   * 获取音频状态信息
   */
  getAudioState() {
    return {
      videoAudioSources: this.videoAudioSources.size,
      timelineAudioSources: this.audioSources.size,
      hasAudioContext: !!this.audioContext,
      audioContextState: this.audioContext?.state || 'closed'
    };
  }

  /**
   * 启动音频电平监控
   */
  private startAudioLevelMonitoring() {
    if (!this.leftAnalyser || !this.rightAnalyser || !this.audioAnalyser) return;

    const analyzeAudioLevels = () => {
      if (!this.leftAnalyser || !this.rightAnalyser || !this.audioAnalyser) return;

      // 获取左声道数据
      const leftBufferLength = this.leftAnalyser.frequencyBinCount;
      const leftDataArray = new Uint8Array(leftBufferLength);
      this.leftAnalyser.getByteFrequencyData(leftDataArray);
      
      const leftTimeDomainData = new Uint8Array(leftBufferLength);
      this.leftAnalyser.getByteTimeDomainData(leftTimeDomainData);

      // 获取右声道数据
      const rightBufferLength = this.rightAnalyser.frequencyBinCount;
      const rightDataArray = new Uint8Array(rightBufferLength);
      this.rightAnalyser.getByteFrequencyData(rightDataArray);
      
      const rightTimeDomainData = new Uint8Array(rightBufferLength);
      this.rightAnalyser.getByteTimeDomainData(rightTimeDomainData);

      // 计算左声道电平
      let leftSum = 0;
      for (let i = 0; i < leftBufferLength; i++) {
        leftSum += leftDataArray[i];
      }
      const leftLevel = (leftSum / leftBufferLength / 255) * 100;

      // 计算右声道电平
      let rightSum = 0;
      for (let i = 0; i < rightBufferLength; i++) {
        rightSum += rightDataArray[i];
      }
      const rightLevel = (rightSum / rightBufferLength / 255) * 100;

      // 计算峰值电平（使用主分析器）
      const mainBufferLength = this.audioAnalyser.frequencyBinCount;
      const mainTimeDomainData = new Uint8Array(mainBufferLength);
      this.audioAnalyser.getByteTimeDomainData(mainTimeDomainData);
      
      let peak = 0;
      for (let i = 0; i < mainTimeDomainData.length; i++) {
        const sample = Math.abs(mainTimeDomainData[i] - 128) / 128;
        peak = Math.max(peak, sample);
      }

      // 更新电平数据 - 只要在播放状态就监控混合后的音频电平
      if (this.isPlaying) {
        this.audioLevelData.left = Math.max(0, Math.min(100, leftLevel));
        this.audioLevelData.right = Math.max(0, Math.min(100, rightLevel));
        this.audioLevelData.peak = Math.max(0, Math.min(100, peak * 100));
      } else {
        this.audioLevelData.left = 0;
        this.audioLevelData.right = 0;
        this.audioLevelData.peak = 0;
      }

      // 通知电平更新
      if (this.onAudioLevelUpdate) {
        timelineDebugger.logCanvasAudioLevels({
          left: this.audioLevelData.left,
          right: this.audioLevelData.right,
          peak: this.audioLevelData.peak,
          isPlaying: this.isPlaying,
          audioSources: this.audioSources.size,
          videoAudioSources: this.videoAudioSources.size,
          hasAnalyser: !!this.audioAnalyser
        }, {
          rawLeftLevel: leftLevel,
          rawRightLevel: rightLevel,
          rawPeak: peak
        });
        
        this.onAudioLevelUpdate({ ...this.audioLevelData });
      } else {
        // 减少警告频率 - 每10秒最多输出一次
        const now = Date.now();
        if (now - this.lastLogTime > 10000) {
          console.warn('⚠️ Canvas渲染引擎 - 音频电平回调未设置');
          this.lastLogTime = now;
        }
      }

      requestAnimationFrame(analyzeAudioLevels);
    };

    analyzeAudioLevels();
  }

  /**
   * 设置音频电平更新回调
   */
  setOnAudioLevelUpdate(callback: (levels: { left: number; right: number; peak: number }) => void) {
    this.onAudioLevelUpdate = callback;
  }

  /**
   * 获取当前音频电平数据
   */
  getAudioLevels() {
    return { ...this.audioLevelData };
  }

  private clearAssets() {
    this.pause();

    this.videoAssets.forEach(asset => {
      const video = asset.element as HTMLVideoElement;
      video.src = '';
      video.load();
    });
    
    this.videoAssets.clear();
    this.audioAssets.clear();
    this.audioSources.clear();
    this.videoAudioSources.clear();
    this.videoGainNodes.clear();
  }

  destroy() {
    this.clearAssets();
    
    if (this.audioContext) {
      this.audioContext.close();
    }
  }

  resize(width: number, height: number) {
    this.canvas.width = width;
    this.canvas.height = height;
    
    if (this.timeline) {
      this.renderFrame(this.currentTime);
    }
  }
}