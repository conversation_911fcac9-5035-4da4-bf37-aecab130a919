/**
 * @file trackDragExample.ts
 * @description 轨道拖拽处理器的使用示例
 *              展示如何在Vue组件中集成专业的轨道拖拽功能
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { createTrackDragHandler, type TrackDragHandler, type DragState, type VisualFeedback } from './trackDragHandler';
import type { TrackType } from './timelineUtils';

/**
 * @composable useTrackDragBehavior
 * @description Vue 3 组合式函数，提供轨道拖拽功能
 */
export function useTrackDragBehavior(props: {
  trackHeight?: number;
  snapThreshold?: number;
  timeline: any;
  onTimelineUpdate: (timeline: any) => void;
}) {
  const { trackHeight = 60, snapThreshold = 15, timeline, onTimelineUpdate } = props;
  
  // 拖拽处理器实例
  let dragHandler: TrackDragHandler;
  
  // 响应式状态
  const dragState = ref<DragState>({ isDragging: false });
  const visualFeedback = ref<VisualFeedback>({ showInsertLine: false });
  const scrollAreaRef = ref<HTMLElement>();
  
  // 轨道显示数据
  const displayTracks = ref<any[]>([]);
  const isDragOverTrackArea = ref(false);
  
  // 初始化拖拽处理器
  onMounted(() => {
    dragHandler = createTrackDragHandler(trackHeight, snapThreshold);
  });
  
  // 清理资源
  onUnmounted(() => {
    if (dragState.value.isDragging) {
      dragHandler?.cancelDrag();
    }
  });

  /**
   * @function startTrackDrag
   * @description 开始轨道拖拽
   */
  const startTrackDrag = (params: {
    event: MouseEvent;
    clip: any;
    trackType: TrackType;
    sourceTrackIndex: number;
    isDraggingFromLibrary?: boolean;
  }) => {
    const { event, clip, trackType, sourceTrackIndex, isDraggingFromLibrary = false } = params;
    
    if (!dragHandler) return;
    
    // 开始拖拽
    dragHandler.startDrag({
      clip,
      trackType,
      sourceTrackIndex,
      startY: event.clientY,
      isDraggingFromLibrary
    });
    
    // 更新状态
    dragState.value = dragHandler.getDragState();
    
    // 添加全局事件监听
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    document.addEventListener('keydown', handleKeyDown);
    
    // 阻止默认行为
    event.preventDefault();
    
    console.log('🎯 开始轨道拖拽');
  };

  /**
   * @function handleDragMove
   * @description 处理拖拽移动
   */
  const handleDragMove = (event: MouseEvent) => {
    if (!dragHandler || !dragState.value.isDragging || !scrollAreaRef.value) return;
    
    try {
      // 获取滚动区域信息
      const scrollAreaRect = scrollAreaRef.value.getBoundingClientRect();
      const scrollTop = scrollAreaRef.value.scrollTop || 0;
      
      // 更新拖拽
      const result = dragHandler.updateDrag({
        mouseY: event.clientY,
        scrollAreaRect,
        scrollTop,
        displayTracks: displayTracks.value,
        allTracks: getAllTracks() // 需要实现获取所有轨道的函数
      });
      
      // 更新状态
      dragState.value = dragHandler.getDragState();
      visualFeedback.value = result.visualFeedback;
      displayTracks.value = result.updatedDisplayTracks;
      
      // 判断是否在轨道区域内
      const relativeY = event.clientY - scrollAreaRect.top + scrollTop;
      isDragOverTrackArea.value = relativeY >= 0 && relativeY <= displayTracks.value.length * trackHeight;
      
    } catch (error) {
      console.error('拖拽移动处理失败:', error);
    }
  };

  /**
   * @function handleDragEnd  
   * @description 处理拖拽结束
   */
  const handleDragEnd = (event: MouseEvent) => {
    if (!dragHandler || !dragState.value.isDragging) return;
    
    // 移除事件监听
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    document.removeEventListener('keydown', handleKeyDown);
    
    // 结束拖拽
    const endResult = dragHandler.endDrag();
    
    if (endResult.success && endResult.dragResult) {
      // 处理成功的拖拽结果
      try {
        // 这里可以根据dragResult来更新timeline
        console.log('🎯 拖拽结果:', endResult.dragResult);
        
        // 如果需要更新timeline，可以在这里调用
        // onTimelineUpdate(updatedTimeline);
        
      } catch (error) {
        console.error('拖拽操作失败:', error);
        // 这里可以显示错误提示
      }
    }
    
    // 重置状态
    dragState.value = { isDragging: false };
    visualFeedback.value = { showInsertLine: false };
    isDragOverTrackArea.value = false;
    
    console.log(endResult.success ? '✅ 拖拽完成' : '❌ 拖拽失败');
  };

  /**
   * @function handleKeyDown
   * @description 处理键盘事件（ESC取消拖拽）
   */
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && dragState.value.isDragging) {
      dragHandler?.cancelDrag();
      
      // 移除事件监听
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('keydown', handleKeyDown);
      
      // 重置状态
      dragState.value = { isDragging: false };
      visualFeedback.value = { showInsertLine: false };
      isDragOverTrackArea.value = false;
      
      console.log('❌ 用户取消拖拽');
    }
  };

  /**
   * @function getAllTracks
   * @description 获取所有轨道数据（需要根据实际数据结构实现）
   */
  const getAllTracks = () => {
    const allTracks: any[] = [];
    
    if (timeline.VideoTracks) {
      allTracks.push(...timeline.VideoTracks);
    }
    if (timeline.AudioTracks) {
      allTracks.push(...timeline.AudioTracks);
    }
    if (timeline.SubtitleTracks) {
      allTracks.push(...timeline.SubtitleTracks);
    }
    
    return allTracks;
  };

  /**
   * @computed 计算属性：插入线样式
   */
  const insertLineStyle = computed(() => {
    if (!visualFeedback.value.showInsertLine) return {};
    
    return {
      display: 'block',
      position: 'absolute' as const,
      left: '0',
      right: '0',
      top: `${visualFeedback.value.insertLinePosition}px`,
      height: '2px',
      backgroundColor: '#00a8ff',
      borderRadius: '1px',
      zIndex: 1000,
      boxShadow: '0 0 4px rgba(0, 168, 255, 0.6)',
      transition: 'all 0.1s ease',
      borderStyle: visualFeedback.value.insertLineStyle || 'solid'
    };
  });

  /**
   * @computed 计算属性：轨道高亮样式
   */
  const getTrackHighlightStyle = computed(() => (trackIndex: number) => {
    if (visualFeedback.value.highlightTrackIndex === trackIndex) {
      return {
        backgroundColor: 'rgba(0, 168, 255, 0.1)',
        borderLeft: '3px solid #00a8ff',
        transition: 'all 0.2s ease'
      };
    }
    return {};
  });

  /**
   * @computed 计算属性：鼠标样式
   */
  const cursorStyle = computed(() => {
    if (dragState.value.isDragging) {
      return visualFeedback.value.cursorStyle || 'grabbing';
    }
    return 'grab';
  });

  // 返回组合式函数的接口
  return {
    // 状态
    dragState: dragState as Readonly<typeof dragState>,
    visualFeedback: visualFeedback as Readonly<typeof visualFeedback>, 
    displayTracks,
    isDragOverTrackArea: isDragOverTrackArea as Readonly<typeof isDragOverTrackArea>,
    
    // 方法
    startTrackDrag,
    
    // 引用
    scrollAreaRef,
    
    // 计算属性
    insertLineStyle,
    getTrackHighlightStyle,
    cursorStyle
  };
}
