<template>
  <el-dialog
    :model-value="visible"
    title="另存为模板"
    width="500px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form :model="form" ref="formRef" label-width="100px" v-loading="loading">
      <el-form-item
        label="模板名称"
        prop="name"
        :rules="[{ required: true, message: '模板名称不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入模板名称"></el-input>
      </el-form-item>
      <el-form-item label="封面URL" prop="coverUrl">
        <el-input v-model="form.coverUrl" placeholder="请输入封面图片URL"></el-input>
      </el-form-item>
      
      <!-- 可变素材预览 -->
      <el-form-item label="可变素材" v-if="variableMaterialsPreview.length > 0">
        <div class="variable-materials-preview">
          <p class="preview-title">检测到以下素材将设置为可替换：</p>
          <div class="materials-list">
            <div 
              v-for="(material, index) in variableMaterialsPreview" 
              :key="index"
              class="material-item"
            >
              <el-tag :type="getTagType(material.type)" size="small">
                {{ material.type }}
              </el-tag>
              <span class="material-title">{{ material.title }}</span>
              <span class="material-duration">{{ formatDuration(material.duration) }}</span>
            </div>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item v-else>
        <div class="no-materials-tip">
          <el-alert
            title="提示"
            description="当前时间轴中没有检测到可变素材，将创建固定模板"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { addTemplate } from '../../api/template';
import type { AddTemplateRequest } from '../../types/template';
import type { Timeline } from '../../types/videoEdit';
import { buildSaveAsTemplateRequest, detectVariableMaterials } from '../../utils/templateUtils';
import { formatTime } from '../../utils/timeUtils';

const props = defineProps<{
  visible: boolean;
  timeline: Timeline | null;
  previewMediaId: string | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const form = reactive({
  name: '',
  coverUrl: '',
});

// 计算可变素材预览
const variableMaterialsPreview = computed(() => {
  return props.timeline ? detectVariableMaterials(props.timeline) : [];
});

// 获取标签类型
const getTagType = (type: string) => {
  switch (type) {
    case 'video': return 'primary';
    case 'audio': return 'success';
    case 'image': return 'warning';
    default: return 'info';
  }
};

// 格式化时长
const formatDuration = (seconds: number) => {
  return formatTime(seconds);
};

watch(() => props.visible, (newVal) => {
  if (newVal) {
    formRef.value?.resetFields();
    form.name = '';
    form.coverUrl = '';
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (!props.timeline) {
        ElMessage.error('无法创建模板，因为缺少Timeline数据。');
        return;
      }

      loading.value = true;
      try {
        // 使用新的工具函数构建模板数据
        const templateData = buildSaveAsTemplateRequest(
          props.timeline, 
          form.name, 
          form.coverUrl || undefined,
          props.previewMediaId
        );
        
        console.log('🚀 发送另存为模板请求:', templateData);
        
        const res = await addTemplate(templateData);
        if (res.code === 200) {
          // 显示检测到的可变素材信息
          const variableMaterials = detectVariableMaterials(props.timeline);
          if (variableMaterials.length > 0) {
            ElMessage.success(`模板创建成功！检测到 ${variableMaterials.length} 个可变素材`);
          } else {
            ElMessage.success('模板创建成功！');
          }
          handleClose();
        } else {
          ElMessage.error(`模板创建失败: ${res.msg}`);
        }
      } catch (error) {
        ElMessage.error('创建模板时发生网络错误');
        console.error('Failed to create template:', error);
      } finally {
        loading.value = false;
      }
    }
  });
};


</script>

<style scoped>
.variable-materials-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.preview-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.materials-list {
  max-height: 200px;
  overflow-y: auto;
}

.material-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.material-item:last-child {
  border-bottom: none;
}

.material-title {
  flex: 1;
  font-size: 13px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-duration {
  font-size: 12px;
  color: #909399;
}

.no-materials-tip {
  width: 100%;
}
</style>