import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV, VITE_BASE_ROUTER } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? VITE_BASE_ROUTER : VITE_BASE_ROUTER,
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
        '@lib': path.resolve(__dirname, './lib'),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 82,
      host: true,
      open: true,  // 启动时是否启动自动打开浏览器
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api': {
          //  target: 'http://***************:8899',
          target: 'http://127.0.0.1:8899',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        },
        '/information-api':{      
          // target: 'http://127.0.0.1:8899',       
          //target: 'http://*************/prod-api',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/information-api/, '')
        },
        '/v3':{
          target: 'http://localhost:8899',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        },
        // **新增**: 阿里云 OSS 媒体资源代理
        '/media-proxy': {
          target: 'https://szb-pc.oss-cn-beijing.aliyuncs.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/media-proxy/, ''),
          configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('🎵 代理媒体请求:', req.url);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              // 设置 CORS 头，允许跨域
              proxyRes.headers['Access-Control-Allow-Origin'] = '*';
              proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, HEAD, OPTIONS';
              proxyRes.headers['Access-Control-Allow-Headers'] = 'Range, Content-Range, Content-Type';
            });
          }
        },
        
        // **备选**: 更宽泛的 OSS 代理（如果有多个 OSS bucket）
        '/oss-proxy': {
          target: 'https://szb-pc.oss-cn-beijing.aliyuncs.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/oss-proxy/, ''),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler'
        }
      },
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    },
    optimizeDeps: {
      include: [
        '@lib/vform/designer.umd.js',
      ]
    },
    build: {
      commonjsOptions: {
        include: /node_modules|lib/
      },
      rollupOptions: {
        treeshake: false
      }
    }
  }
})
