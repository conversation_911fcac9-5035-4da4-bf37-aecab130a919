/**
 * 模板数据调试工具 - 简化版本
 * 提供核心调试功能：Timeline数据分析、媒资上传调试、日志导出
 */

import type { Timeline, TimelineClip } from '../../types/videoEdit';
import UploadDebugger from '../../debug/uploadDebugger';

// 简化的调试配置
interface DebugConfig {
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  enableConsoleOutput: boolean;
  enableAudioLevelLogging: boolean; // 控制音频电平日志
  enableCanvasRenderLogging: boolean; // 控制Canvas渲染日志
  enableTimelineDataLogging: boolean; // 控制Timeline数据日志
  enableStoreStateLogging: boolean; // 控制Store状态日志
  enableLogCaching: boolean; // 控制是否缓存日志
}

// 音频电平监控数据接口
interface AudioLevelData {
  left: number;
  right: number;
  peak: number;
  isPlaying?: boolean;
  audioSources?: number;
  videoAudioSources?: number;
  hasAnalyser?: boolean;
}

class TimelineDebugger {
  private config: DebugConfig = {
    logLevel: 'info',
    enableConsoleOutput: false,
    enableAudioLevelLogging: false, // 默认关闭音频电平日志
    enableCanvasRenderLogging: false, // 默认关闭Canvas渲染日志
    enableTimelineDataLogging: false, // 默认关闭Timeline数据日志
    enableStoreStateLogging: false, // 默认关闭Store状态日志
    enableLogCaching: false // 默认关闭日志缓存
  };

  private logs: Array<{
    timestamp: string;
    level: string;
    message: string;
    data?: any;
  }> = [];

  // 音频电平监控相关
  private lastAudioLevelLogTime = 0;
  private audioLevelStats = {
    totalUpdates: 0,
    playingUpdates: 0,
    maxLevels: { left: 0, right: 0, peak: 0 }
  };

  /**
   * 记录日志
   */
  private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, level, message, data };
    
    // 只有开启日志缓存时才存储
    if (this.config.enableLogCaching) {
      this.logs.push(logEntry);
      
      // 保持日志数量在合理范围内
      if (this.logs.length > 500) {
        this.logs.splice(0, 250);
      }
    }
    
    if (this.config.enableConsoleOutput) {
      const emoji = { error: '❌', warn: '⚠️', info: 'ℹ️', debug: '🔍' };
      console.log(`${emoji[level]} [${timestamp}] ${message}`, data || '');
    }
  }

  /**
   * 获取所有日志
   */
  getLogs() {
    return [...this.logs];
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = [];
    this.audioLevelStats = {
      totalUpdates: 0,
      playingUpdates: 0,
      maxLevels: { left: 0, right: 0, peak: 0 }
    };
    this.log('info', '日志已清除');
  }

  /**
   * 音频电平监控日志 - Canvas渲染引擎端
   */
  logCanvasAudioLevels(levels: AudioLevelData, additionalInfo?: any) {
    if (!this.config.enableAudioLevelLogging) return;

    this.audioLevelStats.totalUpdates++;
    if (levels.isPlaying) {
      this.audioLevelStats.playingUpdates++;
    }

    // 更新最大电平记录
    this.audioLevelStats.maxLevels.left = Math.max(this.audioLevelStats.maxLevels.left, levels.left);
    this.audioLevelStats.maxLevels.right = Math.max(this.audioLevelStats.maxLevels.right, levels.right);
    this.audioLevelStats.maxLevels.peak = Math.max(this.audioLevelStats.maxLevels.peak, levels.peak);

    // 控制日志频率 - 每3秒最多输出一次
    const now = Date.now();
    const shouldLog = now - this.lastAudioLevelLogTime > 3000;

    if (shouldLog) {
      this.log('debug', '🎵 Canvas渲染引擎 - 音频电平数据', {
        levels: {
          left: levels.left.toFixed(2),
          right: levels.right.toFixed(2),
          peak: levels.peak.toFixed(2)
        },
        status: {
          isPlaying: levels.isPlaying,
          audioSources: levels.audioSources || 0,
          videoAudioSources: levels.videoAudioSources || 0,
          hasAnalyser: levels.hasAnalyser
        },
        stats: this.audioLevelStats,
        additionalInfo
      });
      this.lastAudioLevelLogTime = now;
    }
  }

  /**
   * 音频电平监控日志 - Store端
   */
  logStoreAudioLevels(levels: AudioLevelData, enabled: boolean, timestamp: number) {
    if (!this.config.enableAudioLevelLogging) return;

    const now = Date.now();
    const shouldLog = now - this.lastAudioLevelLogTime > 3000;

    if (shouldLog) {
      this.log('debug', '🔊 Store接收音频电平数据', {
        levels,
        enabled,
        timestamp,
        latency: now - timestamp
      });

      if (enabled) {
        this.log('debug', '📊 Store已更新音频电平状态', levels);
      } else {
        this.log('warn', '⚠️ Store - 音频电平监控未启用，跳过更新');
      }
    }
  }

  /**
   * 音频电平监控日志 - Levelmeter组件端
   */
  logComponentAudioLevels(newLevels: AudioLevelData, oldLevels: { left: number; right: number }, timestamp: number) {
    if (!this.config.enableAudioLevelLogging) return;

    const now = Date.now();
    const shouldLog = now - this.lastAudioLevelLogTime > 3000;

    if (shouldLog) {
      this.log('debug', '🎛️ Levelmeter组件接收到音频电平数据', {
        newLevels,
        oldLevels,
        timestamp,
        latency: now - timestamp,
        levelChanged: newLevels.left !== oldLevels.left || newLevels.right !== oldLevels.right
      });
    }
  }

  /**
   * 获取音频监控统计信息
   */
  getAudioLevelStats() {
    return {
      ...this.audioLevelStats,
      config: {
        loggingEnabled: this.config.enableAudioLevelLogging,
        logLevel: this.config.logLevel
      }
    };
  }

  /**
   * 启用/禁用音频电平日志
   */
  setAudioLevelLogging(enabled: boolean) {
    this.config.enableAudioLevelLogging = enabled;
    this.log('info', `音频电平日志已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用Canvas渲染日志
   */
  setCanvasRenderLogging(enabled: boolean) {
    this.config.enableCanvasRenderLogging = enabled;
    this.log('info', `Canvas渲染日志已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用Timeline数据日志
   */
  setTimelineDataLogging(enabled: boolean) {
    this.config.enableTimelineDataLogging = enabled;
    this.log('info', `Timeline数据日志已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用Store状态日志
   */
  setStoreStateLogging(enabled: boolean) {
    this.config.enableStoreStateLogging = enabled;
    this.log('info', `Store状态日志已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用日志缓存
   */
  setLogCaching(enabled: boolean) {
    this.config.enableLogCaching = enabled;
    if (!enabled) {
      this.logs = []; // 禁用缓存时清空现有日志
    }
    // 这条日志总是输出，确保用户知道设置已更改
    const emoji = 'ℹ️';
    const timestamp = new Date().toISOString();
    console.log(`${emoji} [${timestamp}] 日志缓存已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 启用/禁用控制台输出
   */
  setConsoleOutput(enabled: boolean) {
    this.config.enableConsoleOutput = enabled;
    // 这条日志总是输出，确保用户知道设置已更改
    const emoji = enabled ? 'ℹ️' : 'ℹ️';
    const timestamp = new Date().toISOString();
    console.log(`${emoji} [${timestamp}] 控制台输出已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 快速诊断Timeline
   */
  quickDiagnosis(timeline: Timeline): {
    status: 'healthy' | 'warning' | 'error';
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // 检查基本结构
    if (!timeline.VideoTracks || timeline.VideoTracks.length === 0) {
      issues.push('没有视频轨道');
      suggestions.push('确保Timeline包含至少一个视频轨道');
    }

    // 检查文本片段位置
    let textInVideoCount = 0;
    timeline.VideoTracks?.forEach((track) => {
      track.VideoTrackClips.forEach((clip) => {
        if (clip.Type === 'Text') {
          textInVideoCount++;
        }
      });
    });

    if (textInVideoCount > 0) {
      issues.push(`${textInVideoCount} 个文本片段在视频轨道中`);
      suggestions.push('运行Timeline归一化，将文本片段移至字幕轨道');
    }

    // 检查FileUrl
    let missingUrlCount = 0;
    timeline.VideoTracks?.forEach((track) => {
      track.VideoTrackClips.forEach((clip) => {
        if (clip.Type !== 'Text' && (!clip.MediaId || !(clip as any).FileUrl)) {
          missingUrlCount++;
        }
      });
    });

    timeline.AudioTracks?.forEach((track) => {
      track.AudioTrackClips.forEach((clip) => {
        if (!clip.MediaId || !(clip as any).FileUrl) {
          missingUrlCount++;
        }
      });
    });

    if (missingUrlCount > 0) {
      issues.push(`${missingUrlCount} 个片段缺少FileUrl`);
      suggestions.push('调用resolveMediaUrls方法获取缺失的FileUrl');
    }

    // 确定状态
    let status: 'healthy' | 'warning' | 'error' = 'healthy';
    if (issues.length === 0) {
      status = 'healthy';
    } else if (textInVideoCount > 0 || missingUrlCount > 5) {
      status = 'error';
    } else {
      status = 'warning';
    }

    this.log(status === 'error' ? 'error' : status === 'warning' ? 'warn' : 'info', 
             `Timeline诊断完成: ${status}`, { issues, suggestions });

    return { status, issues, suggestions };
  }

  /**
   * 同步上传调试日志
   */
  syncUploadLogs() {
    const uploadDebugger = UploadDebugger.getInstance();
    const uploadLogs = uploadDebugger.getLogs();
    
    // 将上传日志合并到当前调试器
    uploadLogs.forEach(uploadLog => {
      this.logs.push({
        timestamp: uploadLog.timestamp,
        level: uploadLog.level as any,
        message: `[上传] ${uploadLog.message}`,
        data: uploadLog.data
      });
    });

    this.log('info', `已同步 ${uploadLogs.length} 条上传调试日志`);
  }

  /**
   * 清除所有调试日志（包括上传调试）
   */
  clearAllLogs() {
    this.clearLogs();
    UploadDebugger.getInstance().clearLogs();
    this.log('info', '已清除所有调试日志');
  }

  /**
   * 导出完整调试报告
   */
  exportFullDebugReport(): string {
    // 先同步上传日志
    this.syncUploadLogs();

    const report = {
      timestamp: new Date().toISOString(),
      reportType: 'FullDebugReport',
      logs: this.logs,
      summary: {
        totalLogs: this.logs.length,
        errorCount: this.logs.filter(l => l.level === 'error').length,
        warnCount: this.logs.filter(l => l.level === 'warn').length,
        uploadLogs: this.logs.filter(l => l.message.startsWith('[上传]')).length
      },
      uploadDebugData: {
        logs: UploadDebugger.getInstance().getLogs(),
        timestamp: new Date().toISOString()
      }
    };

    return JSON.stringify(report, null, 2);
  }

  /**
   * 下载调试报告
   */
  downloadDebugReport(filename?: string) {
    const reportContent = this.exportFullDebugReport();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const defaultFilename = `debug-report-${timestamp}.json`;
    
    const blob = new Blob([reportContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || defaultFilename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    this.log('info', `调试报告已导出: ${filename || defaultFilename}`);
  }

  /**
   * 自动归一化Timeline
   * 将Text类型的片段从VideoTracks移动到SubtitleTracks
   */
  normalizeTimeline(timeline: Timeline): Timeline {
    this.log('info', '🔄 开始自动归一化Timeline...');
    
    let movedTextClips = 0;
    const normalizedTimeline = JSON.parse(JSON.stringify(timeline)); // 深拷贝
    
    // 确保有字幕轨道
    if (!normalizedTimeline.SubtitleTracks) {
      normalizedTimeline.SubtitleTracks = [];
      this.log('info', '📝 创建了SubtitleTracks数组');
    }
    
    // 确保至少有一个字幕轨道
    if (normalizedTimeline.SubtitleTracks.length === 0) {
      normalizedTimeline.SubtitleTracks.push({
        Id: 0,
        SubtitleTrackClips: []
      });
      this.log('info', '📝 创建了默认字幕轨道(Id=0)');
    }
    
    const subtitleTrack = normalizedTimeline.SubtitleTracks[0]; // 使用第一个字幕轨道
    
    // 处理VideoTracks中的Text片段
    normalizedTimeline.VideoTracks?.forEach((track: any) => {
      const nonTextClips: any[] = [];
      
      track.VideoTrackClips.forEach((clip: any) => {
        if (clip.Type === 'Text') {
          // 转换为字幕片段格式
          const subtitleClip = {
            Type: 'Text',
            Content: clip.Content || clip.Title || '',
            TimelineIn: clip.TimelineIn,
            TimelineOut: clip.TimelineOut,
            Duration: clip.Duration,
            Color: clip.Color || '#FFFFFF',
            FontSize: clip.FontSize || 24,
            FontFamily: clip.FontFamily || 'Arial',
            Position: 'bottom' as const,
            Alignment: 'center' as const
          };
          
          subtitleTrack.SubtitleTrackClips.push(subtitleClip);
          movedTextClips++;
          
          this.log('debug', `📝 移动文本片段到字幕轨道: "${subtitleClip.Content}"`);
        } else {
          nonTextClips.push(clip);
        }
      });
      
      // 更新轨道，移除Text片段
      track.VideoTrackClips = nonTextClips;
    });
    
    // 按时间排序字幕片段
    subtitleTrack.SubtitleTrackClips.sort((a: any, b: any) => a.TimelineIn - b.TimelineIn);
    
    this.log('info', `✅ Timeline归一化完成，移动了${movedTextClips}个文本片段到字幕轨道`);
    
    return normalizedTimeline;
  }

  /**
   * 性能监控工具
   */
  startPerformanceMonitor(operationName: string) {
    const startTime = performance.now();
    this.log('debug', `⏱️ 开始监控性能: ${operationName}`);
    
    return {
      end: () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.log('info', `📊 性能监控结果: ${operationName} 耗时 ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  }

  /**
   * 内存使用情况分析
   */
  analyzeMemoryUsage() {
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      const memoryInfo = {
        usedJSHeapSize: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
        totalJSHeapSize: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
        jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + 'MB'
      };
      
      this.log('info', '🧠 内存使用情况', memoryInfo);
      return memoryInfo;
    } else {
      this.log('warn', '⚠️ 当前浏览器不支持内存监控');
      return null;
    }
  }

  /**
   * 检查Timeline数据结构（从debugHelper.ts迁移）
   */
  debugTimelineData(timeline: any) {
    if (!this.config.enableTimelineDataLogging) return false;

    this.log('info', '🔍 ==================== Timeline数据调试 ====================');
    
    if (!timeline) {
      this.log('error', '❌ Timeline数据为空或未定义');
      return false;
    }
    
    this.log('info', '✅ Timeline基本信息', {
      hasVideoTracks: !!timeline.VideoTracks,
      hasAudioTracks: !!timeline.AudioTracks,
      hasSubtitleTracks: !!timeline.SubtitleTracks,
      videoTracksCount: timeline.VideoTracks?.length || 0,
      audioTracksCount: timeline.AudioTracks?.length || 0,
      subtitleTracksCount: timeline.SubtitleTracks?.length || 0
    });
    
    // 检查视频轨道
    if (timeline.VideoTracks && timeline.VideoTracks.length > 0) {
      timeline.VideoTracks.forEach((track: any, index: number) => {
        this.log('debug', `📹 视频轨道 ${index}`, {
          hasClips: !!track.VideoTrackClips,
          clipsCount: track.VideoTrackClips?.length || 0,
          clips: track.VideoTrackClips?.map((clip: any) => ({
            title: clip.Title || clip.FileName || clip.MediaId,
            timelineIn: clip.TimelineIn,
            timelineOut: clip.TimelineOut,
            duration: clip.TimelineOut - clip.TimelineIn
          }))
        });
      });
    } else {
      this.log('warn', '⚠️ 没有视频轨道数据');
    }
    
    // 检查音频轨道
    if (timeline.AudioTracks && timeline.AudioTracks.length > 0) {
      timeline.AudioTracks.forEach((track: any, index: number) => {
        this.log('debug', `🎵 音频轨道 ${index}`, {
          hasClips: !!track.AudioTrackClips,
          clipsCount: track.AudioTrackClips?.length || 0,
          clips: track.AudioTrackClips?.map((clip: any) => ({
            title: clip.Title || clip.FileName || clip.MediaId,
            timelineIn: clip.TimelineIn,
            timelineOut: clip.TimelineOut,
            duration: clip.TimelineOut - clip.TimelineIn
          }))
        });
      });
    } else {
      this.log('warn', '⚠️ 没有音频轨道数据');
    }
    
    return true;
  }

  /**
   * 检查Store状态（从debugHelper.ts迁移）
   */
  debugStoreState(store: any) {
    if (!this.config.enableStoreStateLogging) return;

    this.log('info', '🏪 Store状态调试');
    
    this.log('debug', 'Store基本状态', {
      hasTimeline: !!store.timeline,
      timelineType: typeof store.timeline,
      isVideoPlaying: store.isVideoPlaying,
      currentTime: store.currentTime,
      videoDurationSeconds: store.videoDurationSeconds
    });
    
    if (store.timeline) {
      this.debugTimelineData(store.timeline);
    }
  }

  /**
   * 检查计算属性状态（从debugHelper.ts迁移）
   */
  debugComputedTracks(tracks: any[], trackType: string) {
    if (!this.config.enableTimelineDataLogging) return;

    this.log('info', `📊 ${trackType}轨道计算属性调试`);
    
    this.log('debug', `${trackType}轨道数量`, tracks.length);
    
    tracks.forEach((track, index) => {
      this.log('debug', `轨道 ${index}`, {
        originalIndex: track.originalIndex,
        hasClips: !!track.clips,
        clipsCount: track.clips?.length || 0,
        clips: track.clips?.map((clip: any, clipIndex: number) => ({
          index: clipIndex,
          name: clip.name,
          start: clip.start,
          duration: clip.duration
        })),
        isPreviewTrack: track.isPreviewTrack,
        isHighlighted: track.isHighlighted
      });
    });
  }

  /**
   * Canvas渲染引擎调试日志
   */
  logCanvasRender(message: string, data?: any) {
    if (!this.config.enableCanvasRenderLogging) return;
    this.log('debug', `🎬 Canvas渲染: ${message}`, data);
  }

  /**
   * 强制输出日志（忽略开关设置）
   * 用于重要信息或错误，始终需要输出
   */
  forceLog(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, level, message, data };
    
    // 始终缓存重要日志
    this.logs.push(logEntry);
    
    // 始终输出到控制台
    const emoji = { error: '❌', warn: '⚠️', info: 'ℹ️', debug: '🔍' };
    console.log(`${emoji[level]} [${timestamp}] [FORCE] ${message}`, data || '');
    
    // 保持日志数量在合理范围内
    if (this.logs.length > 500) {
      this.logs.splice(0, 250);
    }
  }

  /**
   * Canvas渲染性能调试
   */
  logCanvasPerformance(operation: string, duration: number) {
    if (!this.config.enableCanvasRenderLogging) return;
    
    this.log('debug', `⏱️ Canvas性能: ${operation}`, {
      duration: `${duration.toFixed(2)}ms`,
      performance: duration < 16 ? '良好' : duration < 33 ? '一般' : '需优化'
    });
  }

  /**
   * Canvas轨道渲染调试
   */
  logCanvasTrackRender(trackType: string, trackIndex: number, clipsCount: number) {
    if (!this.config.enableCanvasRenderLogging) return;
    
    this.log('debug', `🎭 Canvas轨道渲染: ${trackType}[${trackIndex}]`, {
      clipsCount,
      trackType,
      trackIndex
    });
  }

  /**
   * 调试开关控制方法
   */
  toggleCanvasLogging(enable: boolean) {
    this.config.enableCanvasRenderLogging = enable;
    this.log('info', `🎬 Canvas渲染日志已${enable ? '启用' : '禁用'}`);
  }

  toggleTimelineLogging(enable: boolean) {
    this.config.enableTimelineDataLogging = enable;
    this.log('info', `📊 Timeline数据日志已${enable ? '启用' : '禁用'}`);
  }

  toggleStoreLogging(enable: boolean) {
    this.config.enableStoreStateLogging = enable;
    this.log('info', `🏪 Store状态日志已${enable ? '启用' : '禁用'}`);
  }

  toggleLogCaching(enable: boolean) {
    this.config.enableLogCaching = enable;
    if (!enable) {
      this.logs = [];
    }
    this.log('info', `💾 日志缓存已${enable ? '启用' : '禁用'}`);
  }

  /**
   * 获取当前配置状态
   */
  getDebugStatus() {
    return {
      canvasLogging: this.config.enableCanvasRenderLogging,
      timelineLogging: this.config.enableTimelineDataLogging,
      storeLogging: this.config.enableStoreStateLogging,
      logCaching: this.config.enableLogCaching,
      cachedLogsCount: this.logs.length
    };
  }
}

// 创建全局调试器实例
const timelineDebugger = new TimelineDebugger();

// 导出调试器实例供其他模块使用
export { timelineDebugger };

/**
 * 调试Timeline数据结构（简化版）
 */
export function debugTimelineStructure(rawTimeline: Timeline) {
  console.log('🔍 ==================== Timeline 数据调试 ====================');
  console.log('📊 原始Timeline数据:', rawTimeline);

  const stats = {
    videoTracks: rawTimeline.VideoTracks?.length || 0,
    audioTracks: rawTimeline.AudioTracks?.length || 0,
    subtitleTracks: rawTimeline.SubtitleTracks?.length || 0,
    textClipsInVideoTracks: 0
  };

  // 分析VideoTracks中的片段类型
  rawTimeline.VideoTracks?.forEach((track, trackIndex) => {
    console.log(`📹 VideoTrack ${trackIndex}: ${track.VideoTrackClips.length} 个片段`);
    
    track.VideoTrackClips.forEach((clip, clipIndex) => {
      if (clip.Type === 'Text') {
        stats.textClipsInVideoTracks++;
        console.log(`  🔤 字幕片段 ${clipIndex}: "${(clip as any).Content}"`);
      } else {
        console.log(`  🎬 视频片段 ${clipIndex}: ${(clip as any).Title}, MediaId: ${clip.MediaId}`);
      }
    });
  });

  // 分析AudioTracks
  rawTimeline.AudioTracks?.forEach((track, trackIndex) => {
    console.log(`🎵 AudioTrack ${trackIndex}: ${track.AudioTrackClips.length} 个片段`);
  });

  console.log(' 统计结果:', stats);

  if (stats.textClipsInVideoTracks > 0) {
    console.log(`⚠️ 发现问题: VideoTracks中有 ${stats.textClipsInVideoTracks} 个Text类型片段`);
    console.log('💡 建议: 需要运行Timeline归一化');
  }

  return stats;
}

/**
 * 调试MediaId和FileUrl情况（简化版）
 */
export function debugMediaIdAndFileUrl(timeline: Timeline) {
  console.log('🔗 ==================== MediaId & FileUrl 调试 ====================');
  
  const stats = {
    clipsWithFileUrl: 0,
    clipsWithoutFileUrl: 0,
    textClips: 0
  };

  // 检查视频片段
  timeline.VideoTracks?.forEach((track) => {
    track.VideoTrackClips.forEach((clip) => {
      if (clip.Type === 'Text') {
        stats.textClips++;
      } else if (clip.MediaId) {
        if ((clip as any).FileUrl) {
          stats.clipsWithFileUrl++;
        } else {
          stats.clipsWithoutFileUrl++;
          console.log(`❌ 缺少FileUrl: ${clip.MediaId}`);
        }
      }
    });
  });

  // 检查音频片段
  timeline.AudioTracks?.forEach((track) => {
    track.AudioTrackClips.forEach((clip) => {
      if (clip.MediaId) {
        if ((clip as any).FileUrl) {
          stats.clipsWithFileUrl++;
        } else {
          stats.clipsWithoutFileUrl++;
          console.log(`❌ 缺少FileUrl: ${clip.MediaId}`);
        }
      }
    });
  });

  console.log('📊 MediaId统计:', stats);
  return stats;
}

/**
 * 启动调试模式（简化版）
 */
export function startDebugMode() {
  console.log('🚀 调试模式已启动');
  console.log('💡 使用方法:');
  console.log('  - window.debugTools.diagnose(timeline) // 诊断Timeline');
  console.log('  - window.debugTools.downloadReport() // 下载调试报告');
  console.log('  - window.debugTools.clearAllLogs() // 清除所有日志');
}

// 导出给控制台使用（简化版）
(window as any).debugTools = {
  // Timeline分析
  debugTimelineStructure,
  debugMediaIdAndFileUrl,
  
  // 调试功能
  diagnose: (timeline: Timeline) => timelineDebugger.quickDiagnosis(timeline),
  normalizeTimeline: (timeline: Timeline) => timelineDebugger.normalizeTimeline(timeline),
  getLogs: () => timelineDebugger.getLogs(),
  clearAllLogs: () => timelineDebugger.clearAllLogs(),
  downloadReport: (filename?: string) => timelineDebugger.downloadDebugReport(filename),
  startPerformanceMonitor: (name: string) => timelineDebugger.startPerformanceMonitor(name),
  analyzeMemory: () => timelineDebugger.analyzeMemoryUsage(),
  
  // 音频电平监控调试
  getAudioStats: () => timelineDebugger.getAudioLevelStats(),
  enableAudioLogging: () => timelineDebugger.setAudioLevelLogging(true),
  disableAudioLogging: () => timelineDebugger.setAudioLevelLogging(false),
  
  // 控制台输出控制
  enableConsoleOutput: () => timelineDebugger.setConsoleOutput(true),
  disableConsoleOutput: () => timelineDebugger.setConsoleOutput(false),
  getConfig: () => timelineDebugger.getConfig(),
  
  // 上传调试器
  uploadDebugger: UploadDebugger.getInstance(),
  
  // 调试器实例
  debugger: timelineDebugger
};

// 自动启动调试模式（在开发环境）
if (import.meta.env?.DEV) {
  startDebugMode();
  console.log('🎯 开发环境自动启用调试模式');
  console.log('📋 快速使用:');
  console.log('  window.debugTools.downloadReport() // 一键导出调试报告');
  console.log('  window.debugTools.getAudioStats() // 查看音频监控统计');
  console.log('  window.debugTools.disableAudioLogging() // 禁用音频日志（减少输出）');
  console.log('  window.debugTools.enableAudioLogging() // 启用音频日志');
  console.log('  window.debugTools.disableConsoleOutput() // 禁用控制台输出（静默模式）');
  console.log('  window.debugTools.enableConsoleOutput() // 启用控制台输出');
}


// // 查看当前配置
// window.debugTools.getConfig()

// // 禁用控制台输出（静默模式）
// window.debugTools.disableConsoleOutput()

// // 启用控制台输出（调试模式）
// window.debugTools.enableConsoleOutput()

// // 即使禁用控制台输出，仍可获取所有日志
// window.debugTools.getLogs()

// // 或下载完整报告
// window.debugTools.downloadReport()
