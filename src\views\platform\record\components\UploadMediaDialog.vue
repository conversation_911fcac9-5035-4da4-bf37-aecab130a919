<template>
    <div>
        <el-dialog
            :model-value="visible"
            title="上传本地音视频文件"
            width="800px"
            :align-center="true"
            :close-on-click-modal="false"
            :close-on-press-escape="true"
            @close="handleClose"
            @update:model-value="(val) => emits('update:visible', val)"
        >
            <div class="dialog-body">
                <div class="dialog-left">
                    <div class="upload-box">
                        <el-upload ref="upload" :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove"
                            :file-list="fileList" :limit="1"
                            accept=".mp4,.wmv,.m4v,.flv,.rmvb,.dat,.mov,.mkv,.webm,.avi,.mpeg,.3gp,.ogg,.mp3,.wav,.m4a,.wma,.aac,.ogg,.amr,.flac,.aiff"
                            drag style="width: 100%; height: 100%;">
                            <div v-if="!fileUploaded" class="upload-empty-state">
                                <div class="upload-desc">
                                    <span class="upload-link">点击</span> / <span class="upload-link">拖拽</span> 本地音视频文件到这里
                                </div>
                                <ul class="upload-tips">
                                    <li>单个文件最长6小时，仅支持上传1个文件。</li>
                                    <li>视频支持：mp4/wmv/m4v/flv/rmvb/dat/mov/ <br />mkv/webm/avi/mpeg/3gp/ogg，单个最大6G；</li>
                                    <li>音频支持：mp3/wav/m4a/wma/aac/ogg/amr<br />/flac/aiff，单个最大500M。</li>
                                </ul>
                            </div>

                        </el-upload>

                    </div>
                </div>
                <div class="dialog-right">
                    <el-form :model="form" ref="formRef">
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="音视频语言"></el-form-item>
                            <el-radio-group v-model="form.language">
                                <el-radio v-for="item in langOptions" :key="item.value" :label="item.value"
                                    size="middle" border>
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="翻译"></el-form-item>
                            <el-select v-model="form.translate" size="large">
                                <el-option v-for="item in translateOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="区分发言人"></el-form-item>
                            <el-radio-group v-model="form.speakerType">
                                <el-radio v-for="item in speakerOptions" :key="item.value" :label="item.value"
                                    size="middle" border>
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="词表选择"></el-form-item>
                            <el-select v-model="form.phraseId" size="large" placeholder="请选择词表" clearable
                                :loading="phraseLoading" class="phrase-select">
                                <el-option v-for="item in phraseList" :key="item.phraseId" :label="item.name"
                                    :value="item.phraseId" class="phrase-option">
                                    <div class="option-content">
                                        <span class="option-name">{{ item.name }}</span>
                                    </div>
                                </el-option>
                            </el-select>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit" :disabled="!fileUploaded">开始转写</el-button>
                            <el-button @click="handleClose">取消</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { reactive, toRefs, defineProps, defineEmits, ref, watch, onMounted } from 'vue'

import { summitOfflineTask } from '@/api/tingwu/record'
import { listPhrase } from '@/api/tingwu/phrase'

import { ElMessage } from 'element-plus'

const props = defineProps({
    visible: Boolean
})

// 添加监听器来调试visible属性变化
watch(() => props.visible, (newVal) => {
    console.log('UploadMediaDialog visible changed to:', newVal)
})
const emits = defineEmits(['update:visible', 'refresh-data'])

const translateOptions = [
    { value: '0', label: '不翻译' },
    { value: '1', label: '英语' },
    { value: '2', label: '日语' },
]

const langOptions = [
    { label: '中文', value: '0' },
    { label: '英语', value: '1' },
    { label: '日语', value: '2' },
    { label: '粤语', value: '3' },
    { label: '中英文自由说', value: '4' }
]
const speakerOptions = [
    { label: '暂不体验', value: '0' },
    { label: '单人演讲', value: '1' },
    { label: '2人对话', value: '2' },
    { label: '多人讨论', value: '3' }
]

// 文件上传相关逻辑
const fileUploaded = ref(false)
const fileList = ref([])

const formRef = ref()

// 词表相关数据
const phraseList = ref([])
const phraseLoading = ref(false)

const data = reactive({
    form: {
        language: '0',
        translate: '0', // 默认不翻译
        speakerType: '0',
        phraseId: '' // 添加词表选择字段
    }
});

const { form } = toRefs(data);

// 获取词表列表
const getPhraseList = async () => {
    try {
        phraseLoading.value = true
        const response = await listPhrase({ pageNum: 1, pageSize: 999 })
        phraseList.value = response.rows || []
    } catch (error) {
        console.error('获取词表列表失败:', error)
        ElMessage.error('获取词表列表失败')
    } finally {
        phraseLoading.value = false
    }
}

// 组件挂载时获取词表数据
onMounted(() => {
    getPhraseList()
})

/**文件被改变 */
const handleChange = (file, fileListRaw) => {
    fileList.value = fileListRaw
    changeFileUploadedValue()
}

function handleClose() {
    // 重置表单和文件列表
    resetForm()
    // 关闭对话框
    emits('update:visible', false)
    // 通知父组件刷新数据
    emits('refresh-data')
}

/**删除文件 */
const handleRemove = (file, fileListRaw) => {
    fileList.value = fileListRaw
    changeFileUploadedValue()
}

/**修改数据 */
const changeFileUploadedValue = () => {
    fileList.value.length > 0 ? fileUploaded.value = true : fileUploaded.value = false
}

/**重置表单 */
const resetForm = () => {
    // 重置表单字段
    if (formRef.value) {
        formRef.value.resetFields()
    }

    // 手动重置表单值到默认值
    form.value.language = '0'
    form.value.translate = '0'
    form.value.speakerType = '0'
    form.value.phraseId = ''

    // 清空文件列表
    fileList.value = []
    fileUploaded.value = false
}

const onSubmit = async () => {
    try {
        const formData = new FormData();
        if (fileList.value.length > 0) {
            formData.append('files', fileList.value[0].raw);
        }
        formData.append('language', form.value.language);
        formData.append('translate', form.value.translate);
        formData.append('speakerType', form.value.speakerType);
        if (form.value.phraseId) {
            formData.append('phraseId', form.value.phraseId);
        }
        await summitOfflineTask(formData)
        ElMessage.success('上传成功');
        // openTask();
    } catch (error) {
        ElMessage.error('上传失败');
    } finally {
        resetForm();
        handleClose();
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

:deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
    border-radius: 10px;
    .el-dialog__title { font-size: 18px; font-weight: 600; color: white; }
}

:deep(.el-dialog__headerbtn) {
    top: 20px; right: 20px; width: 32px; height: 32px;
    background: rgba(255, 255, 255, 0.2); border-radius: 50%; transition: all 0.3s ease;
    &:hover { background: rgba(255, 255, 255, 0.3); transform: scale(1.1); }
    .el-dialog__close { color: white; font-size: 16px; }
}

:deep(.el-dialog__body) { height: 500px; padding: 16px; background: #fafbfc; overflow: hidden; }


.dialog-body { display: flex; gap: 24px; height: 100%; }
.dialog-left { flex: 1.2; display: flex; align-items: center; justify-content: center; }

.upload-box {
    width: 100%; max-width: 380px; min-height: 400px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fd 100%);
    border: 2px dashed #e1e8ff; border-radius: 20px;
    display: flex; justify-content: flex-start; flex-direction: column;
    position: relative; transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);

    &:hover {
        border-color: #667eea; transform: translateY(-2px);
        box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15);
    }

    :deep(.el-upload) { width: 100%; height: 100%; display: flex; flex-direction: column; }

    :deep(.el-upload-dragger) {
        width: 100%; height: 100%; background-color: transparent; border: none;
        border-radius: 20px; transition: all 0.3s ease; display: flex;
        flex-direction: column; justify-content: center; align-items: center;
        .el-upload__text { display: none; }
        &:hover { background-color: rgba(102, 126, 234, 0.05); }
    }

    :deep(.el-upload-list) {
        margin: 0; padding: 0; order: -1; flex-shrink: 0; width: 100%; box-sizing: border-box;
        &:not(:empty)+.el-upload .el-upload-dragger { display: none !important; }
    }

    &:has(.el-upload-list__item) {
        :deep(.el-upload-dragger) { display: none !important; }
    }

    :deep(.el-upload-list__item) {
        margin: 16px 16px 0 16px; width: calc(100% - 32px); box-sizing: border-box;
        background: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
        border-radius: 16px; border: 2px solid #e1e8ff;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.08);
        transition: all 0.3s ease; padding: 20px; position: relative; overflow: hidden;

        &:last-child { margin-bottom: 16px; }
        &::before {
            content: ""; position: absolute; top: 0; left: 0; right: 0; height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        &:hover {
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px); border-color: #667eea;
        }

        .el-upload-list__item-info { display: flex; align-items: center; gap: 16px; }
        .el-upload-list__item-name {
            color: #2d3748; font-weight: 600; font-size: 15px; margin: 0; flex: 1;
            display: flex; align-items: center; gap: 12px;
            &::before { content: "🎵"; font-size: 24px; display: inline-block; }
        }
        .el-upload-list__item-status-label { display: none; }
        .el-upload-list__item-delete {
            position: absolute; top: 12px; right: 12px; width: 24px; height: 24px;
            border-radius: 50%; background: rgba(255, 77, 79, 0.1);
            display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;
            &:hover { background: rgba(255, 77, 79, 0.2); transform: scale(1.1); }
            .el-icon { color: #ff4d4f; font-size: 14px; }
        }
    }

    .uploaded-file {
        text-align: center; padding: 24px;
        .file-preview { max-width: 100%; max-height: 200px; margin-bottom: 16px; border-radius: 12px; }
        .file-name { font-size: 16px; color: #2d3748; font-weight: 600; margin-bottom: 8px; }
        .file-info {
            display: flex; justify-content: center; gap: 16px; margin-top: 12px;
            .file-size, .file-type {
                padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;
            }
            .file-size { background: #f0f4ff; color: #667eea; }
            .file-type { background: #f0fff4; color: #38a169; }
        }
    }

    :deep(.el-upload-list__item.is-success) { animation: slideInUp 0.5s ease-out; }
    @keyframes slideInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
}

.upload-empty-state {
    display: flex; flex-direction: column; justify-content: center;
    align-items: center; height: 100%; padding: 40px 20px;
}

.upload-icon {
    margin-bottom: 20px; text-align: center;
    &::before { content: "📁"; font-size: 48px; display: block; margin-bottom: 8px; opacity: 0.7; }
}

.upload-desc {
    display: flex; justify-content: center; align-items: center;
    color: #4a5568; font-size: 16px; font-weight: 500; margin-bottom: 24px; text-align: center;
    .upload-link {
        color: #667eea; cursor: pointer; font-weight: 600; transition: color 0.3s ease;
        &:hover { color: #5a67d8; }
    }
}

.upload-tips {
    color: #718096; font-size: 13px; line-height: 1.6; margin: 0; padding: 0 24px;
    list-style: none; display: flex; flex-direction: column; gap: 8px;
    li {
        position: relative; padding-left: 16px;
        &::before { content: "•"; color: #667eea; font-weight: bold; position: absolute; left: 0; }
    }
}

.dialog-right {
    flex: 1; display: flex; flex-direction: column; background: white;
    border-radius: 16px; padding: 20px 16px 16px 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); justify-content: flex-start;
    min-height: 0; max-height: 100%; overflow: hidden;
}

:deep(.el-form) {
    width: 100%; height: 100%; display: flex; flex-direction: column;
    overflow-y: auto; overflow-x: hidden; scroll-behavior: smooth;
    &::-webkit-scrollbar { width: 0px; background: transparent; }
    &::-webkit-scrollbar-thumb { background: transparent; }
    scrollbar-width: none; -ms-overflow-style: none;

    .el-row {
        margin-bottom: 0; flex-shrink: 0;
        &:not(:last-child) { margin-bottom: 12px; }
    }

    .el-form-item {
        margin-bottom: 3px;
        .el-form-item__label {
            color: #2d3748; font-size: 13px; font-weight: 600;
            margin-bottom: 3px; line-height: 1.2; display: block;
        }
        &:last-child {
            margin-top: 16px; margin-bottom: 0; padding-top: 8px; flex-shrink: 0;
        }
    }

    .el-radio-group {
        display: grid; grid-template-columns: repeat(2, 1fr); gap: 4px;
        &:first-of-type {
            grid-template-columns: repeat(2, 1fr);
            .el-radio:nth-child(5) { grid-column: 1 / -1; justify-self: start; }
        }
        &:last-of-type { grid-template-columns: repeat(2, 1fr); }
        .el-radio {
            margin-right: 0;
            &.is-bordered {
                border-radius: 4px; border-color: #e2e8f0; padding: 3px 6px;
                font-size: 11px; text-align: center; min-height: 24px;
                display: flex; align-items: center; justify-content: center;
                &.is-checked {
                    border-color: #667eea; background-color: #667eea; color: white;
                    .el-radio__label { color: white; }
                }
            }
        }
    }

    .el-select {
        width: 100% !important;
        .el-input__wrapper {
            border-radius: 4px; border-color: #e2e8f0; height: 24px; font-size: 12px;
            &.is-focus { border-color: #667eea; }
        }
        .el-input__inner { font-size: 12px; }
    }

    .phrase-select .el-input__wrapper { height: 32px; }

    .el-button {
        border-radius: 4px; padding: 5px 14px; font-weight: 500;
        font-size: 11px; flex: 1; height: 28px;
        &.el-button--primary {
            background: linear-gradient(135deg, #8b9cf7 0%, #9bb5ff 100%);
            border: none; color: white;
            &:disabled { background: #e2e8f0; color: #a0aec0; cursor: not-allowed; }
        }
        &.el-button--default { border-color: #e2e8f0; color: #4a5568; }
    }
}

:deep(.el-select-dropdown) {
    .phrase-option {
        padding: 0 !important;
        .option-content {
            padding: 8px 12px;
            .option-name { font-size: 13px; font-weight: 500; color: #2d3748; line-height: 1.2; }
        }
        &:hover .option-content .option-name { color: #667eea; }
        &.selected .option-content .option-name { color: #667eea; font-weight: 600; }
    }
}
</style>
