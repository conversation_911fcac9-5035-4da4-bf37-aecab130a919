<template>
    <div class="professional-audio-meter">
        <div class="meter-header">
            <div class="meter-title">AUDIO LEVELS</div>
        </div>
        
        <div class="meter-body">
            <!-- 刻度值标记 -->
            <div class="scale-indicators">
                <div 
                    v-for="scale in scaleMarks" 
                    :key="scale.value"
                    class="scale-mark"
                    :style="{ top: scale.position + '%' }"
                >
                    <span class="scale-value">{{ scale.label }}</span>
                    <div class="scale-line"></div>
                </div>
            </div>
            
            <!-- 电平条容器 -->
            <div class="meter-channels">
                <!-- 左声道 -->
                <div class="channel-container">
                    <div class="channel-meter" ref="leftMeter">
                        <div 
                            v-for="(segment, index) in meterSegments" 
                            :key="`left-${index}`"
                            class="meter-segment"
                            :class="[
                                segment.color,
                                { 'active': leftLevel >= segment.threshold }
                            ]"
                            :style="{ 
                                top: segment.position + '%',
                                height: segment.height + '%'
                            }"
                        ></div>
                    </div>
                    <div class="channel-label">L</div>
                </div>
                
                <!-- 右声道 -->
                <div class="channel-container">
                    <div class="channel-meter" ref="rightMeter">
                        <div 
                            v-for="(segment, index) in meterSegments" 
                            :key="`right-${index}`"
                            class="meter-segment"
                            :class="[
                                segment.color,
                                { 'active': rightLevel >= segment.threshold }
                            ]"
                            :style="{ 
                                top: segment.position + '%',
                                height: segment.height + '%'
                            }"
                        ></div>
                    </div>
                    <div class="channel-label">R</div>
                </div>
            </div>
            
            <!-- 数字显示 -->
            <div class="digital-display">
                <div class="channel-reading">
                    <span class="channel-name">L</span>
                    <span class="level-value" :class="getLevelColorClass(leftLevel)">{{ formatLevelValue(leftLevel) }}</span>
                </div>
                <div class="channel-reading">
                    <span class="channel-name">R</span>
                    <span class="level-value" :class="getLevelColorClass(rightLevel)">{{ formatLevelValue(rightLevel) }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useVideoEditorStore } from '../useVideoEditor'

// 定义组件的 props
const props = defineProps({
    enabled: {
        type: Boolean,
        default: true
    }
})

// 获取视频编辑器Store
const videoEditorStore = useVideoEditorStore()

// 响应式数据，用于存储左右声道的音频电平
const leftLevel = ref(0)
const rightLevel = ref(0)

// 从Store获取音频电平数据
const audioLevels = computed(() => videoEditorStore.audioLevels)
const isPlaying = computed(() => videoEditorStore.isVideoPlaying)
const isMonitoringEnabled = computed(() => videoEditorStore.audioLevelUpdateEnabled)

// 专业音频电平表刻度标记（基于标准dB刻度）
const scaleMarks = [
    { value: 0, label: '0', position: 0 },      // 数字满电平 (危险线)
    { value: -3, label: '-3', position: 10 },   // 标准峰值电平
    { value: -6, label: '-6', position: 27 },   // 安全峰值电平  
    { value: -9, label: '-9', position: 39 },   // 高音量
    { value: -12, label: '-12', position: 48 }, // 正常音量
    { value: -15, label: '-15', position: 57 }, // 中等音量
    { value: -18, label: '-18', position: 66 }, // 对话电平
    { value: -24, label: '-24', position: 75 }, // 低音量
    { value: -30, label: '-30', position: 82 }, // 很低音量
    { value: -36, label: '-36', position: 90 }, // 背景音电平
    { value: -42, label: '-∞', position: 95 }   // 接近静音
]

// 电平表分段配置（按照专业dB标准，精确匹配转换逻辑）
const meterSegments = [
    // 红色危险区域 (0dB to -3dB) - 过载风险
    { threshold: 95, color: 'red', position: 0, height: 6 },      // -0.5dB
    { threshold: 89, color: 'red', position: 6, height: 4 },      // -1dB
    { threshold: 71, color: 'red', position: 10, height: 5 },     // -3dB
    
    // 橙色警告区域 (-3dB to -6dB) - 需要注意
    { threshold: 67, color: 'orange', position: 15, height: 3 },   // -3.5dB
    { threshold: 63, color: 'orange', position: 18, height: 3 },   // -4dB
    { threshold: 59, color: 'orange', position: 21, height: 3 },   // -4.5dB
    { threshold: 56, color: 'orange', position: 24, height: 3 },   // -5dB
    { threshold: 50, color: 'orange', position: 27, height: 3 },   // -6dB
    
    // 黄色注意区域 (-6dB to -12dB) - 标准峰值区
    { threshold: 47, color: 'yellow', position: 30, height: 3 },   // -6.5dB
    { threshold: 44, color: 'yellow', position: 33, height: 3 },   // -7dB
    { threshold: 39, color: 'yellow', position: 36, height: 3 },   // -8dB
    { threshold: 35, color: 'yellow', position: 39, height: 3 },   // -9dB
    { threshold: 32, color: 'yellow', position: 42, height: 3 },   // -10dB
    { threshold: 28, color: 'yellow', position: 45, height: 3 },   // -11dB
    { threshold: 25, color: 'yellow', position: 48, height: 3 },   // -12dB
    
    // 绿色安全区域 (-12dB to -24dB) - 正常工作电平
    { threshold: 22, color: 'green', position: 51, height: 3 },    // -13dB
    { threshold: 20, color: 'green', position: 54, height: 3 },    // -14dB
    { threshold: 18, color: 'green', position: 57, height: 3 },    // -15dB
    { threshold: 16, color: 'green', position: 60, height: 3 },    // -16dB
    { threshold: 14, color: 'green', position: 63, height: 3 },    // -17dB
    { threshold: 12, color: 'green', position: 66, height: 3 },    // -18dB (对话电平)
    { threshold: 10, color: 'green', position: 69, height: 3 },    // -20dB
    { threshold: 8, color: 'green', position: 72, height: 3 },     // -22dB
    { threshold: 7, color: 'green', position: 75, height: 3 },     // -24dB
    
    // 深绿色低电平区域 (-24dB以下) - 背景音/环境音
    { threshold: 5, color: 'green-dim', position: 78, height: 4 }, // -28dB
    { threshold: 4, color: 'green-dim', position: 82, height: 4 }, // -30dB
    { threshold: 3, color: 'green-dim', position: 86, height: 4 }, // -33dB
    { threshold: 2, color: 'green-dim', position: 90, height: 5 }, // -36dB
    { threshold: 1, color: 'green-dim', position: 95, height: 5 }  // -42dB
]

// 将电平值（0-100）转换为专业dB显示
const formatLevelValue = (level: number): string => {
    if (level <= 0) return '-∞'
    
    // 专业音频电平转换公式：将线性电平值转换为对数dB值
    // 100 = 0dB (数字满电平)，每降低约6个单位 ≈ -6dB
    
    // 精确的dB转换映射表 (基于专业音频标准)
    if (level >= 100) return '+0'  // 过载警告
    if (level >= 95) return '-0.5'  // 接近满电平
    if (level >= 89) return '-1'    // 很接近满电平
    if (level >= 84) return '-1.5'  
    if (level >= 79) return '-2'    
    if (level >= 75) return '-2.5'  
    if (level >= 71) return '-3'    // 标准峰值电平
    if (level >= 67) return '-3.5'  
    if (level >= 63) return '-4'    
    if (level >= 59) return '-4.5'  
    if (level >= 56) return '-5'    
    if (level >= 53) return '-5.5'  
    if (level >= 50) return '-6'    // 安全峰值电平
    if (level >= 47) return '-6.5'  
    if (level >= 44) return '-7'    
    if (level >= 42) return '-7.5'  
    if (level >= 39) return '-8'    
    if (level >= 37) return '-8.5'  
    if (level >= 35) return '-9'    // 黄色警告区开始
    if (level >= 33) return '-9.5'  
    if (level >= 32) return '-10'   
    if (level >= 30) return '-10.5' 
    if (level >= 28) return '-11'   
    if (level >= 26) return '-11.5' 
    if (level >= 25) return '-12'   // 正常音量电平
    if (level >= 24) return '-12.5' 
    if (level >= 22) return '-13'   
    if (level >= 21) return '-13.5' 
    if (level >= 20) return '-14'   
    if (level >= 19) return '-14.5' 
    if (level >= 18) return '-15'   // 绿色安全区开始
    if (level >= 17) return '-15.5' 
    if (level >= 16) return '-16'   
    if (level >= 15) return '-16.5' 
    if (level >= 14) return '-17'   
    if (level >= 13) return '-17.5' 
    if (level >= 12) return '-18'   // 对话电平
    if (level >= 11) return '-19'   
    if (level >= 10) return '-20'   
    if (level >= 9) return '-21'    
    if (level >= 8) return '-22'    
    if (level >= 7) return '-24'    // 低音量
    if (level >= 6) return '-26'    
    if (level >= 5) return '-28'    
    if (level >= 4) return '-30'    // 很低音量
    if (level >= 3) return '-33'    
    if (level >= 2) return '-36'    // 非常低音量
    if (level >= 1) return '-42'    // 几乎静音
    
    return '-∞'  // 完全静音
}

// 根据电平值确定显示颜色类
const getLevelColorClass = (level: number): string => {
    if (level <= 0) return 'silent'          // 静音：灰色
    if (level >= 71) return 'danger'         // >= -3dB：红色 (危险)
    if (level >= 35) return 'warning'        // -3dB to -9dB：黄色 (警告)
    if (level >= 1) return 'safe'            // -9dB以下：绿色 (安全)
    return 'silent'                          // 默认静音
}

// 监听Store中的音频电平数据变化
watch(audioLevels, (newLevels) => {
    leftLevel.value = newLevels.left
    rightLevel.value = newLevels.right
}, { deep: true })

// 监听播放状态，当停止播放时重置电平
watch(isPlaying, (playing) => {
    if (!playing) {
        leftLevel.value = 0
        rightLevel.value = 0
    }
})

// 启用音频监控
const enableMonitoring = () => {
    videoEditorStore.enableAudioLevelMonitoring()
}

// 禁用音频监控
const disableMonitoring = () => {
    videoEditorStore.disableAudioLevelMonitoring()
    leftLevel.value = 0
    rightLevel.value = 0
}

// 暴露给父组件的方法
defineExpose({
    enableMonitoring,
    disableMonitoring,
    getAudioLevels: () => ({ left: leftLevel.value, right: rightLevel.value }),
    isMonitoringEnabled: () => isMonitoringEnabled.value
})

// 组件挂载后，根据props启用/禁用监控
onMounted(() => {
    if (props.enabled) {
        enableMonitoring()
    }
})

// 组件卸载时，清理监控
onUnmounted(() => {
    if (isMonitoringEnabled.value) {
        disableMonitoring()
    }
})
</script>

<style lang="scss" scoped>
.professional-audio-meter {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 120px; /* 固定宽度，符合专业软件标准 */
    background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #333;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #e0e0e0;
}

.meter-header {
    padding: 6px 8px 4px;
    border-bottom: 1px solid #333;
    background: linear-gradient(180deg, #2a2a2a 0%, #1f1f1f 100%);
    
    .meter-title {
        font-size: 9px;
        font-weight: 600;
        letter-spacing: 0.5px;
        color: #b0b0b0;
        text-align: center;
        text-transform: uppercase;
    }
}

.meter-body {
    flex: 1;
    display: flex;
    position: relative;
    padding: 8px 4px 4px;
    min-height: 200px;
}

.scale-indicators {
    position: absolute;
    left: 4px;
    top: 8px;
    bottom: 32px;
    width: 20px;
    z-index: 2;
    
    .scale-mark {
        position: absolute;
        display: flex;
        align-items: center;
        width: 100%;
        height: 1px;
        
        .scale-value {
            font-size: 8px;
            font-weight: 500;
            color: #888;
            width: 16px;
            text-align: right;
            line-height: 1;
        }
        
        .scale-line {
            width: 4px;
            height: 1px;
            background-color: #444;
            margin-left: 1px;
        }
    }
}

.meter-channels {
    display: flex;
    gap: 6px;
    margin-left: 24px;
    flex: 1;
    
    .channel-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        
        .channel-meter {
            position: relative;
            width: 16px;
            height: 100%;
            background: #111;
            border: 1px solid #333;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 6px;
            
            .meter-segment {
                position: absolute;
                width: 100%;
                background: #222;
                transition: background-color 0.05s linear, box-shadow 0.05s linear;
                border-radius: 0;
                
                &.active {
                    &.red {
                        background: linear-gradient(180deg, #ff4444 0%, #cc3333 100%);
                        box-shadow: 0 0 4px rgba(255, 68, 68, 0.6);
                    }
                    
                    &.yellow {
                        background: linear-gradient(180deg, #ffcc00 0%, #e6b800 100%);
                        box-shadow: 0 0 3px rgba(255, 204, 0, 0.5);
                    }
                    
                    &.orange {
                        background: linear-gradient(180deg, #ff8800 0%, #e67700 100%);
                        box-shadow: 0 0 3px rgba(255, 136, 0, 0.5);
                    }
                    
                    &.green {
                        background: linear-gradient(180deg, #44ff44 0%, #33cc33 100%);
                        box-shadow: 0 0 2px rgba(68, 255, 68, 0.4);
                    }
                    
                    &.green-dim {
                        background: linear-gradient(180deg, #22aa22 0%, #1a8a1a 100%);
                        box-shadow: 0 0 2px rgba(34, 170, 34, 0.3);
                    }
                }
                
                /* 非激活状态的暗色背景 */
                &:not(.active) {
                    &.red { background: #331111; }
                    &.yellow { background: #332a00; }
                    &.orange { background: #331f00; }
                    &.green { background: #113311; }
                    &.green-dim { background: #0a220a; }
                }
            }
        }
        
        .channel-label {
            font-size: 10px;
            font-weight: 600;
            color: #bbb;
            text-align: center;
            width: 16px;
        }
    }
}

.digital-display {
    position: absolute;
    bottom: 4px;
    left: 4px;
    right: 4px;
    height: 24px;
    background: #0a0a0a;
    border: 1px solid #333;
    border-radius: 2px;
    display: flex;
    
    .channel-reading {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        padding: 2px;
        
        &:not(:last-child) {
            border-right: 1px solid #333;
        }
        
        .channel-name {
            font-size: 8px;
            font-weight: 600;
            color: #888;
            min-width: 8px;
        }
        
        .level-value {
            font-size: 9px;
            font-weight: 500;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #0f0; /* 默认绿色 */
            text-shadow: 0 0 3px rgba(0, 255, 0, 0.3);
            min-width: 24px;
            text-align: right;
        }
        
        /* 通过Vue动态类来控制颜色，而不是CSS的:contains */
        .level-value.danger {
            color: #f44 !important;
            text-shadow: 0 0 3px rgba(255, 68, 68, 0.5) !important;
        }
        
        .level-value.warning {
            color: #fc0 !important;
            text-shadow: 0 0 3px rgba(255, 204, 0, 0.4) !important;
        }
        
        .level-value.silent {
            color: #666 !important;
            text-shadow: none !important;
        }
    }
}

/* 响应式调整 */
@media (max-height: 300px) {
    .professional-audio-meter {
        .scale-indicators .scale-value {
            font-size: 7px;
        }
        
        .meter-channels .channel-container .channel-label {
            font-size: 9px;
        }
        
        .digital-display .channel-reading {
            .channel-name { font-size: 7px; }
            .level-value { font-size: 8px; }
        }
    }
}
</style>