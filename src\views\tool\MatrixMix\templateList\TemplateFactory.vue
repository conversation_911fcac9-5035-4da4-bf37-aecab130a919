<template>
  <div class="template-factory-container">
    <el-card shadow="hover" class="factory-card">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Film /></el-icon>
          <span>模板工厂</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="factory-tabs">
        <el-tab-pane label="普通模板" name="normal">
          <div class="tab-content">
            <div class="actions-bar">
              <el-form :model="queryParams" :inline="true" class="search-form">
                <el-form-item>
                  <el-input v-model="queryParams.keyword" placeholder="请输入模板ID或名称" class="search-input" clearable
                    @clear="handleQuery" @keyup.enter="handleQuery">
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-select v-model="queryParams.status" placeholder="模板状态" clearable class="search-input">
                    <el-option label="启用" value="Available"></el-option>
                    <el-option label="停用" value="Unavailable"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery" class="search-btn">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="resetQuery" class="reset-btn">
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
              <div class="action-buttons">
                <el-button type="primary" @click="createTemplate" class="create-btn">
                  <el-icon><el-icon-plus /></el-icon>
                  创建模板
                </el-button>
              </div>
            </div>

            <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange"
              style="width: 100%" class="template-table" border stripe>
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="模板" min-width="250">
                <template #default="{ row }">
                  <div class="template-info">
                    <div class="template-cover">
                      <el-image v-if="row.CoverURL" :src="row.CoverURL" fit="cover" class="template-image" />
                      <el-icon v-else>
                        <Film />
                      </el-icon>
                    </div>
                    <div class="template-details">
                      <div class="template-name">{{ row.Name }}</div>
                      <div class="template-id">ID: {{ row.TemplateId }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Status" label="状态" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.Status === 'Available' ? 'success' : 'info'" effect="plain">{{ row.Status === 'Available' ? '启用' :
                    '停用' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="CreateSource" label="创建方式" width="150" align="center"/>
              <el-table-column prop="CreationTime" label="创建时间" width="180" align="center">
                <template #default="scope">
                  <div class="time-info">
                    <div>{{ parseTime(scope.row.CreationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="ModifiedTime" label="更新时间" width="180" align="center">
                <template #default="scope">
                  <div class="time-info">
                    <div>{{ parseTime(scope.row.ModifiedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="240" fixed="right" align="center">
                <template #default="{ row }">
                  <el-button link type="primary" @click="useTemplate(row)" class="table-action">使用模板</el-button>
                  <el-button link type="primary" @click="editTemplate(row)" class="table-action">编辑模板</el-button>
                  <el-button link type="danger" @click="deleteTemplate(row)" class="table-action">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination v-if="total > 0" v-model:current-page="queryParams.pageNo"
              v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total"
              layout="total, sizes, prev, pager, next, jumper" @size-change="fetchTemplateList"
              @current-change="fetchTemplateList" class="pagination-container" />
          </div>
        </el-tab-pane>
      </el-tabs>

    </el-card>
    <!-- 创建模板弹窗子组件 -->
    <CreateTemplateDialog v-model:visible="dialogVisible" @success="fetchTemplateList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { UploadRawFile, UploadRequestOptions } from 'element-plus';
import { Search, Refresh, Film } from '@element-plus/icons-vue';
import { listTemplates, addTemplate, getTemplate, deleteTemplate as deleteTemplateApi } from '../api/template';
import { uploadFileUnified } from '@/api/file/info.js';
import type { TemplateInfo, ListTemplatesRequest, AddTemplateRequest } from '../types/template';  
import ConfigBuilder from './components/ConfigBuilder.vue';
import { useRouter } from 'vue-router';
import { ElLoading } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';
// 新增：引入创建模板弹窗子组件
import CreateTemplateDialog from './components/CreateTemplateDialog.vue';

const activeTab = ref('normal');
const loading = ref(false);
const templateList = ref<TemplateInfo[]>([]);
const multipleSelection = ref<TemplateInfo[]>([]);
const total = ref(0);

// --- 新增：创建模板弹窗相关 ---
const dialogVisible = ref(false);

const data = reactive<{
  queryParams: ListTemplatesRequest
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 5,
    keyword: '',
    status: '',
    type: 'Timeline', // 默认只查询Timeline类型的模板
  }
});

const { queryParams } = toRefs(data);
const router = useRouter();

const fetchTemplateList = async () => {
  loading.value = true;
  try {
    const res = await listTemplates(queryParams.value);
    if (res.code === 200) {
      // 兼容后端未分页和已分页两种情况
      const allTemplates = res.data.Templates || [];
      const totalCount = res.data.TotalCount || allTemplates.length;
      total.value = totalCount;
      const { pageNo = 1, pageSize = 10 } = queryParams.value;
      // 如果后端返回的Templates数量大于pageSize，且totalCount大于pageSize，说明未分页，需要前端slice
      if (allTemplates.length > pageSize && totalCount > pageSize) {
        const start = (pageNo - 1) * pageSize;
        const end = start + pageSize;
        templateList.value = allTemplates.slice(start, end);
      } else {
        // 后端已分页，直接用
        templateList.value = allTemplates;
      }
    } else {
      ElMessage.error(`获取模板列表失败: ${res.msg}`);
      templateList.value = [];
      total.value = 0;
    }
  } catch (error) {
    ElMessage.error('获取模板列表时发生错误');
    templateList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.value.pageNo = 1;
  fetchTemplateList();
};

const resetQuery = () => {
  queryParams.value.pageNo = 1;
  queryParams.value.keyword = '';
  queryParams.value.status = '';
  fetchTemplateList();
};

const createTemplate = () => {
  dialogVisible.value = true;
};

const useTemplate = (row: TemplateInfo) => {
  ElMessage.info(`使用模板: ${row.Name}`);
};

const editTemplate = async (row: TemplateInfo) => {
  if (!row.TemplateId) {
    ElMessage.error('模板ID缺失，无法编辑。');
    return;
  }

  const loadingInstance = ElLoading.service({
    lock: true, // 全屏锁定
    text: '正在加载模板详情，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 调用 getTemplate API 获取模板详细信息
    const res = await getTemplate(row.TemplateId);
    if (res.code === 200 && res.data?.Template) {
      // 成功获取模板详情后，跳转到视频编辑页
      router.push({
        path: `/tool/matrix-mix/video-edit/${row.TemplateId}`, // 修改为完整路径
        query: { from: 'template-factory' } // 添加查询参数，告知来源是模板工厂
      });
      ElMessage.success(`正在跳转到模板 [${row.Name}] 编辑页`);
    } else {
      ElMessage.error(`获取模板 [${row.Name}] 详情失败: ${res.msg || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取模板详情时发生错误:', error);
    ElMessage.error(`加载模板 [${row.Name}] 详情时发生网络错误`);
  } finally {
    loadingInstance.close(); // 关闭加载提示
  }
};

const deleteTemplate = (row: TemplateInfo) => {
  ElMessageBox.confirm(`确定要删除模板 "${row.Name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      // 调用 API 删除模板，templateIds 参数是逗号分隔的字符串
      const res = await deleteTemplateApi({ templateIds: row.TemplateId });
      if (res.code === 200) {
        ElMessage.success('删除成功');
        fetchTemplateList(); // 刷新列表
      } else {
        ElMessage.error(`删除失败: ${res.msg || '未知错误'}`);
      }
    } catch (error) {
      console.error('删除模板时发生错误:', error);
      ElMessage.error('删除模板时发生网络错误');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleSelectionChange = (val: TemplateInfo[]) => {
  multipleSelection.value = val;
};


onMounted(() => {
  fetchTemplateList();
});
</script>

<style lang="scss" scoped>
.template-factory-container {
  padding: 32px 24px 24px 24px;
  background: #f7f8fa;
  min-height: 100vh;
}

.factory-card {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.06);
  border: none;
  overflow: visible;
}

.card-header {
  font-size: 22px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #222;
  padding: 8px 0 4px 0;
}
.header-icon {
  font-size: 28px;
  color: var(--el-color-primary);
}

.factory-tabs {
  margin-top: 8px;
  .el-tabs__header {
    margin-bottom: 0;
  }
}

.tab-content {
  margin-top: 12px;
  .actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 18px;
    background: #fff;
    border-radius: 10px;
    padding: 18px 18px 10px 18px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
    .search-form {
      display: flex;
      align-items: flex-end;
      gap: 8px;
    }
    .search-input {
      width: 220px;
      border-radius: 8px;
    }
    .search-btn, .reset-btn {
      border-radius: 8px;
      min-width: 80px;
    }
    .create-btn {
      border-radius: 8px;
      font-weight: 600;
      min-width: 120px;
      box-shadow: 0 2px 8px 0 rgba(64,158,255,0.08);
    }
  }
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .template-table {
    border-radius: 10px;
    overflow: hidden;
    font-size: 15px;
    .el-table__header th {
      background: #f5f7fa;
      color: #333;
      font-weight: 600;
    }
    .el-table__row {
      transition: background 0.2s;
      &:hover {
        background: #f0f7ff !important;
      }
    }
    .el-table__body tr.current-row {
      background: #e6f7ff !important;
    }
    .table-action {
      font-size: 15px;
      margin: 0 2px;
      padding: 0 6px;
      border-radius: 6px;
      transition: background 0.2s;
      &:hover {
        background: #f0f7ff;
      }
    }
  }
  .template-info {
    display: flex;
    align-items: center;
    .template-cover {
      width: 48px;
      height: 48px;
      background-color: #f0f2f5;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 14px;
      font-size: 26px;
      color: #bfcbd9;
      overflow: hidden;
      box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
      .template-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .template-details {
      .template-name {
        font-weight: 600;
        font-size: 16px;
        color: #222;
      }
      .template-id {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
  .time-info {
    color: #666;
    font-size: 13px;
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  padding: 10px 0 2px 0;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
}

// 弹窗美化
.factory-dialog {
  .el-dialog__header {
    font-size: 20px;
    font-weight: 700;
    color: #222;
    padding-bottom: 0;
  }
  .el-dialog__body {
    padding: 24px 32px 8px 32px;
    background: #f7f8fa;
    border-radius: 0 0 16px 16px;
  }
  .dialog-form {
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-input, .el-select {
      border-radius: 8px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 10px 32px 18px 32px;
  }
}

// 上传封面美化
.cover-uploader .el-upload {
  border: 1.5px dashed #d9d9d9;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  background: #fafbfc;
  width: 148px;
  height: 148px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cover-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
  background: #f0f7ff;
}
.cover-uploader-icon {
  font-size: 32px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  text-align: center;
  line-height: 148px;
}
.cover-image {
  width: 148px;
  height: 148px;
  display: block;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  object-fit: cover;
}
</style>