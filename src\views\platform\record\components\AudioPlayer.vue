<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
import useAppStore from '@/store/modules/app'

// 定义props
const props = defineProps({
  url: {
    type: String,
    required: true
  },
  conversations: {
    type: Array,
    default: () => []
  }
})

// 定义emits
const emit = defineEmits(['timeUpdate'])

// 获取应用状态
const appStore = useAppStore()

// 音频元素引用
const audioRef = ref(null)

// 内部状态管理
const isPlaying = ref(false)
const currentTime = ref(0)
const totalTime = ref(0) // 总时长（秒）
const playbackSpeed = ref('1.0x') // 播放倍速显示
const isLoading = ref(false)
const isError = ref(false)

// 高频时间更新定时器
let timeUpdateTimer = null

// 章节速览相关状态
const showChapterPreview = ref(false)
const chapterPreviewData = ref(null)
const previewPosition = ref({ x: 0, y: 0 })

// 监听侧边栏状态变化
const sidebarWidth = computed(() => {
  return appStore.sidebar.opened ? '200px' : '54px'
})

// 动态更新CSS变量
watch(sidebarWidth, (newWidth) => {
  const audioPlayer = document.querySelector('.audio-player')
  if (audioPlayer) {
    audioPlayer.style.setProperty('--sidebar-width', newWidth)
  }
}, { immediate: true })

// 格式化时间函数（显示为 MM:SS 格式）
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 音频事件处理
const handleLoadedMetadata = () => {
  if (audioRef.value) {
    totalTime.value = Math.floor(audioRef.value.duration)
    isLoading.value = false
  }
}

const handleTimeUpdate = () => {
  if (audioRef.value) {
    // 保持更高精度的时间显示
    currentTime.value = Math.floor(audioRef.value.currentTime)
    // 发出时间更新事件，传递精确的当前时间（秒），保留小数点精度
    emit('timeUpdate', audioRef.value.currentTime)
  }
}

// 启动高频时间更新定时器
const startTimeUpdateTimer = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
  }
  // 每50ms更新一次时间，确保词汇高亮更加精确
  timeUpdateTimer = setInterval(() => {
    if (audioRef.value && isPlaying.value) {
      emit('timeUpdate', audioRef.value.currentTime)
    }
  }, 50)
}

// 停止高频时间更新定时器
const stopTimeUpdateTimer = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
    timeUpdateTimer = null
  }
}

const handlePlay = () => {
  isPlaying.value = true
  startTimeUpdateTimer()
}

const handlePause = () => {
  isPlaying.value = false
  stopTimeUpdateTimer()
}

const handleEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
  // 同步音频元素的currentTime到0，确保快进/回退功能正常
  if (audioRef.value) {
    audioRef.value.currentTime = 0
  }
  stopTimeUpdateTimer()
}

const handleError = () => {
  isError.value = true
  isLoading.value = false
  console.error('音频加载失败')
}

// 播放/暂停切换
const handleTogglePlay = async () => {
  if (!audioRef.value) return

  try {
    if (isPlaying.value) {
      audioRef.value.pause()
    } else {
      await audioRef.value.play()
    }
  } catch (error) {
    console.error('播放失败:', error)
    isError.value = true
  }
}

// 快退功能
const handleRewind = () => {
  if (!audioRef.value) return
  audioRef.value.currentTime = Math.max(0, audioRef.value.currentTime - 10)
}

// 快进功能
const handleFastForward = () => {
  if (!audioRef.value) return
  audioRef.value.currentTime = Math.min(totalTime.value, audioRef.value.currentTime + 10)
}

// 进度条拖拽
const handleProgressChange = (event) => {
  if (!audioRef.value) return

  const progressBar = event.currentTarget
  const rect = progressBar.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * totalTime.value

  audioRef.value.currentTime = newTime
}

// 根据时间查找对应的对话章节
const findConversationByTime = (timeInSeconds) => {
  if (!props.conversations || props.conversations.length === 0) return null

  const timeInMs = timeInSeconds * 1000

  // 查找包含该时间点的对话
  for (let i = 0; i < props.conversations.length; i++) {
    const conversation = props.conversations[i]
    if (timeInMs >= conversation.startTime && timeInMs <= conversation.endTime) {
      return {
        ...conversation,
        index: i
      }
    }
  }

  // 如果没有找到精确匹配，找最近的对话
  let closestConversation = null
  let minDistance = Infinity

  for (let i = 0; i < props.conversations.length; i++) {
    const conversation = props.conversations[i]
    const startDistance = Math.abs(timeInMs - conversation.startTime)
    const endDistance = Math.abs(timeInMs - conversation.endTime)
    const distance = Math.min(startDistance, endDistance)

    if (distance < minDistance) {
      minDistance = distance
      closestConversation = {
        ...conversation,
        index: i
      }
    }
  }

  return closestConversation
}

// 处理进度条鼠标悬浮
const handleProgressHover = (event) => {
  if (!audioRef.value || totalTime.value === 0) return

  const progressBar = event.currentTarget
  const rect = progressBar.getBoundingClientRect()
  const hoverX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(1, hoverX / rect.width))
  const hoverTime = percentage * totalTime.value

  // 查找对应的对话章节
  const conversation = findConversationByTime(hoverTime)

  if (conversation && conversation.text && conversation.text.trim()) {
    // 有文字内容，显示章节速览
    chapterPreviewData.value = {
      ...conversation,
      hoverTime: hoverTime,
      percentage: percentage
    }

    // 设置预览框位置 - 显示在进度条上方，避免覆盖
    const previewWidth = 250 // 预估预览框宽度
    const screenWidth = window.innerWidth
    let xPosition = event.clientX

    // 防止预览框超出屏幕右边界
    if (xPosition + previewWidth / 2 > screenWidth) {
      xPosition = screenWidth - previewWidth / 2 - 10
    }
    // 防止预览框超出屏幕左边界
    if (xPosition - previewWidth / 2 < 0) {
      xPosition = previewWidth / 2 + 10
    }

    previewPosition.value = {
      x: xPosition,
      y: rect.top - 10 // 显示在进度条上方，留出10px间距
    }

    showChapterPreview.value = true
  } else {
    // 没有文字内容，隐藏预览
    showChapterPreview.value = false
  }
}

// 处理进度条鼠标离开
const handleProgressLeave = () => {
  showChapterPreview.value = false
  chapterPreviewData.value = null
}

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 倍速切换
const toggleSpeed = () => {
  if (!audioRef.value) return

  const speeds = ['1.0x', '1.25x', '1.5x', '2.0x']
  const speedValues = [1.0, 1.25, 1.5, 2.0]
  const currentIndex = speeds.indexOf(playbackSpeed.value)
  const nextIndex = (currentIndex + 1) % speeds.length

  playbackSpeed.value = speeds[nextIndex]
  audioRef.value.playbackRate = speedValues[nextIndex]
}

// 监听播放状态变化
const togglePlay = () => {
  handleTogglePlay()
}

// 跳转到指定时间（毫秒）
const seekToTime = (milliseconds) => {
  if (!audioRef.value) return

  const seconds = milliseconds / 1000
  audioRef.value.currentTime = Math.min(seconds, totalTime.value)

  // 如果音频没有在播放，则开始播放
  if (!isPlaying.value) {
    handleTogglePlay()
  }
}

// 暴露方法给父组件
defineExpose({
  seekToTime,
  togglePlay,
  isPlaying: () => isPlaying.value,
  getCurrentTime: () => currentTime.value,
  getTotalTime: () => totalTime.value
})

// 初始化音频
const initAudio = () => {
  if (audioRef.value && props.url) {
    isLoading.value = true
    isError.value = false
    audioRef.value.src = props.url
    audioRef.value.load()
  }
}

// 监听URL变化
watch(() => props.url, (newUrl) => {
  if (newUrl) {
    initAudio()
  }
}, { immediate: true })

// 组件挂载时初始化CSS变量和音频
onMounted(() => {
  const audioPlayer = document.querySelector('.audio-player')
  if (audioPlayer) {
    audioPlayer.style.setProperty('--sidebar-width', sidebarWidth.value)
  }

  // 初始化音频
  if (props.url) {
    initAudio()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  stopTimeUpdateTimer()
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.src = ''
  }
})
</script>

<template>
  <div class="audio-player">
    <!-- 隐藏的音频元素 -->
    <audio
      ref="audioRef"
      @loadedmetadata="handleLoadedMetadata"
      @timeupdate="handleTimeUpdate"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
      @error="handleError"
      preload="metadata"
    ></audio>

    <!-- 顶部进度条 -->
    <div class="progress-section">
      <div
        class="progress-container"
        @click="handleProgressChange"
        @mousemove="handleProgressHover"
        @mouseleave="handleProgressLeave"
      >
        <div class="progress-track">
          <div
            class="progress-fill"
            :style="{ width: totalTime > 0 ? (currentTime / totalTime * 100) + '%' : '0%' }"
          ></div>
          <div
            class="progress-thumb"
            :style="{ left: totalTime > 0 ? (currentTime / totalTime * 100) + '%' : '0%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 章节速览预览框 -->
    <teleport to="body">
      <div
        v-if="showChapterPreview && chapterPreviewData"
        class="chapter-preview"
        :style="{
          left: previewPosition.x + 'px',
          top: previewPosition.y + 'px'
        }"
      >
        <div class="preview-header">
          <div class="preview-time">
            {{ formatTimestamp(chapterPreviewData.startTime) }} - {{ formatTimestamp(chapterPreviewData.endTime) }}
          </div>
          <div class="preview-speaker" v-if="chapterPreviewData.speakerName">
            {{ chapterPreviewData.speakerName }}
          </div>
        </div>
        <div class="preview-content">
          {{ chapterPreviewData.text }}
        </div>
        <!-- 指向进度条的小箭头 -->
        <div class="preview-arrow"></div>
      </div>
    </teleport>

    <!-- 底部控制区域 -->
    <div class="controls-row">
      <!-- 左侧：喇叭图标和时间显示 -->
      <div class="left-section">
        <div class="volume-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19.07 4.93C20.9447 6.80528 21.9979 9.34836 21.9979 12C21.9979 14.6516 20.9447 17.1947 19.07 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M15.54 8.46C16.4774 9.39764 17.0039 10.6692 17.0039 12C17.0039 13.3308 16.4774 14.6024 15.54 15.54" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="time-display">
          {{ formatTime(currentTime) }}/{{ formatTime(totalTime) }}
        </div>
      </div>

      <!-- 中间：播放控制按钮 -->
      <div class="control-section">
        <button class="control-btn rewind-btn" @click="handleRewind">
          <el-icon>
            <DArrowLeft />
          </el-icon>
        </button>
        <button class="play-btn" @click="togglePlay" :disabled="isLoading || isError || !props.url">
          <el-icon v-if="!isLoading">
            <VideoPlay v-if="!isPlaying" />
            <VideoPause v-else />
          </el-icon>
          <div v-else class="loading-spinner">⏳</div>
        </button>
        <button class="control-btn forward-btn" @click="handleFastForward">
          <el-icon>
            <DArrowRight />
          </el-icon>
        </button>
      </div>

      <!-- 右侧：倍速按钮 -->
      <div class="right-section">
        <button class="speed-btn" @click="toggleSpeed" :disabled="isLoading || isError || !props.url">
          {{ playbackSpeed }}
        </button>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div v-if="isError" class="error-message">
      音频加载失败，请检查音频链接
    </div>
  </div>
</template>

<style scoped>
.audio-player {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  position: fixed;
  bottom: 16px;
  left: calc(var(--sidebar-width) + 16px);
  width: calc((100% - var(--sidebar-width) - 64px - 32px) * 0.6 - 1px);
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
}

/* 响应侧边栏状态 */
:global(.hideSidebar) .audio-player {
  --sidebar-width: 54px;
}

:global(.openSidebar) .audio-player {
  --sidebar-width: 200px;
}

/* 默认状态 */
.audio-player {
  --sidebar-width: 200px;
}

/* 顶部进度条区域 */
.progress-section {
  width: 100%;
  padding: 16px 24px 8px;
}

.progress-container {
  position: relative;
  height: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.progress-container:hover {
  transform: scaleY(1.2);
}

.progress-track {
  width: 100%;
  height: 6px;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 8px;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 8px;
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border: 3px solid #3b82f6;
  border-radius: 50%;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.progress-thumb:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow:
    0 6px 16px rgba(59, 130, 246, 0.4),
    0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 底部控制区域 */
.controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px 16px;
  height: 56px;
}

/* 左侧区域：喇叭图标和时间 */
.left-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.volume-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  color: #3b82f6;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.volume-icon:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.time-display {
  font-size: 14px;
  color: #475569;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  min-width: 90px;
  padding: 6px 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

/* 中间控制按钮区域 */
.control-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.control-btn:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.play-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.play-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.play-btn:active {
  transform: translateY(-1px) scale(1.02);
}

/* 右侧区域：倍速按钮 */
.right-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.speed-btn {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid rgba(148, 163, 184, 0.2);
  color: #475569;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.speed-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.speed-btn:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #0369a1;
  border-color: rgba(3, 105, 161, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(3, 105, 161, 0.2);
}

.speed-btn:hover::before {
  left: 100%;
}

/* 加载状态 */
.loading-spinner {
  animation: spin 1.5s linear infinite;
  font-size: 18px;
  color: #3b82f6;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 音频播放器入场动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.audio-player {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 进度条脉冲动画 */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 2px 16px rgba(59, 130, 246, 0.5);
  }
}

.progress-fill {
  animation: pulse 2s ease-in-out infinite;
}

/* 禁用状态 */
.play-btn:disabled,
.speed-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-btn:disabled:hover,
.speed-btn:disabled:hover {
  background: transparent;
  color: inherit;
}

/* 错误信息 */
.error-message {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #f56565;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

/* 章节速览预览框样式 */
.chapter-preview {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  max-width: 300px;
  min-width: 200px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  pointer-events: none;
  transform: translateX(-50%) translateY(-100%);
  animation: fadeInDown 0.2s ease-out;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-time {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.preview-speaker {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.preview-content {
  font-size: 14px;
  line-height: 1.4;
  color: #f8fafc;
  word-break: break-word;
  /* 限制为两行文字 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.9);
}

/* 预览框动画 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) translateY(0);
  }
}

/* 进度条悬浮增强 */
.progress-container:hover .progress-track {
  height: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
}

.progress-container:hover .progress-fill {
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.4);
}
</style>
