<template>
    <div class="record-page">
        <!-- 顶部操作区 -->
        <RecordHeader :is-selection-mode="isSelectionMode" :selected-items="selectedItems" :current-view="currentView"
            :current-sort="currentSort" :show-advanced-filter="showAdvancedFilter" @create-command="handleCreateCommand"
            @view-command="handleViewCommand" @sort-command="handleSortCommand"
            @toggle-advanced-filter="toggleAdvancedFilter" @batch-delete="handleBatchDelete"
            @toggle-selection-mode="toggleSelectionMode" />

        <UploadMediaDialog :visible="uploadDialogVisible" @update:visible="uploadDialogVisible = $event"
            @refresh-data="handleRefreshData" />

        <!-- 高级筛选区域 -->
        <AdvancedFilter :visible="showAdvancedFilter" v-model="filterParams" @search="handleSearch"
            @clear="clearFilters" @filter-change="handleFilterChange" />

        <!-- 卡片列表区 -->
        <RecordList :data-list="dataList" :is-selection-mode="isSelectionMode" :selected-items="selectedItems"
            :current-view="currentView" @item-select="handleItemSelect" @card-click="handleCardClick" />

        <!-- 分页区 -->
        <RecordPagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
            @pagination="handlePagination" />
    </div>
</template>



<script setup>
import { ref, reactive, toRefs, onMounted, onUnmounted, nextTick } from 'vue'
import RecordHeader from './components/RecordHeader.vue'
import AdvancedFilter from './components/AdvancedFilter.vue'
import RecordList from './components/RecordList.vue'
import RecordPagination from './components/RecordPagination.vue'
import UploadMediaDialog from './components/UploadMediaDialog.vue'
import { getTingwuTransList, getStatusAndResultByTaskId, getTingwuTrans, delTingwuTrans } from "@/api/tingwu/record"


const uploadDialogVisible = ref(false)
const statusCheckTimer = ref(null)

import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

function handleCreateCommand(cmd) {
    console.log('handleCreateCommand called with:', cmd)
    if (cmd === 'upload') {
        console.log('Setting uploadDialogVisible to true')
        uploadDialogVisible.value = true
        console.log('uploadDialogVisible is now:', uploadDialogVisible.value)
    }
}

/**处理视图命令 */
const handleViewCommand = (command) => {
    currentView.value = command
}

/**处理排序命令 */
const handleSortCommand = (command) => {
    currentSort.value = command
    getList()
}

const loading = ref(true)
const total = ref(0)
const dataList = ref([])
const isCheckingStatus = ref(false)

// 批量删除相关状态
const isSelectionMode = ref(false)
const selectedItems = ref([])

// 高级筛选相关状态
const showAdvancedFilter = ref(false)
const filterParams = ref({
    vaName: null,
    startTime: null,
    endTime: null
})

// 排序相关状态
const currentSort = ref('createTime_desc') // 默认按创建时间降序排列

// 视图相关状态
const currentView = ref('grid') // 默认网格视图

const data = reactive({
    form: {
        imageId: null,
    },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
    rules: {
    }
})

const { queryParams, form, rules } = toRefs(data)


/**获取列表数据 */
const getList = async () => {
    loading.value = true
    try {
        // 解析排序参数
        const [sortField, sortOrder] = currentSort.value.split('_')

        // 合并查询参数、筛选参数和排序参数
        const params = {
            ...queryParams.value,
            vaName: filterParams.value.vaName,
            startTime: filterParams.value.startTime,
            endTime: filterParams.value.endTime,
            sortField: sortField,
            sortOrder: sortOrder
        }

        console.log('getList 请求参数:', params)
        console.log('当前分页参数:', {
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize
        })

        const response = await getTingwuTransList(params)
        dataList.value = response.rows
        total.value = response.total

        console.log('数据加载完成:', {
            total: response.total,
            rows: response.rows?.length,
            currentPage: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize
        })
    } catch (error) {
        console.error('获取列表失败:', error)
        proxy.$modal.msgError("获取列表失败: " + (error.message || "未知错误"))
    } finally {
        loading.value = false
    }
}

/**处理数据刷新 */
const handleRefreshData = async () => {
    console.log('收到刷新数据请求，重新加载列表')
    await getList()
    console.log('数据刷新完成')
}

/**切换高级筛选显示状态 */
const toggleAdvancedFilter = () => {
    if (showAdvancedFilter.value) {
        // 收起筛选时清除所有筛选条件
        filterParams.value.vaName = null
        filterParams.value.startTime = null
        filterParams.value.endTime = null
        // 重新获取数据
        getList()
    }
    showAdvancedFilter.value = !showAdvancedFilter.value
}

/**查询功能 */
const handleSearch = () => {
    // 执行查询
    getList()
}

/**清除筛选条件 */
const clearFilters = () => {
    filterParams.value.vaName = null
    filterParams.value.startTime = null
    filterParams.value.endTime = null
    // 重新获取数据
    getList()
}

/**处理筛选条件变化 */
const handleFilterChange = () => {
    // 筛选条件变化时不自动查询，需要用户点击查询按钮
    // 这样可以让用户设置完所有条件后再查询
}

/**处理分页事件 */
const handlePagination = (paginationData) => {
    console.log('收到分页事件:', paginationData)

    // 如果传入了分页数据，使用传入的数据更新queryParams
    if (paginationData) {
        queryParams.value.pageNum = paginationData.page
        queryParams.value.pageSize = paginationData.limit
        console.log('更新分页参数:', {
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize
        })
    }

    // 使用nextTick确保参数更新后再发起请求
    nextTick(() => {
        getList()
    })
}



/**切换选择模式 */
const toggleSelectionMode = () => {
    isSelectionMode.value = !isSelectionMode.value
    if (!isSelectionMode.value) {
        selectedItems.value = []
    }
}

/**处理单项选择 */
const handleItemSelect = (vaId, checked) => {
    if (checked) {
        if (!selectedItems.value.includes(vaId)) {
            selectedItems.value.push(vaId)
        }
    } else {
        const index = selectedItems.value.indexOf(vaId)
        if (index > -1) {
            selectedItems.value.splice(index, 1)
        }
    }
}

/**处理卡片点击 */
const handleCardClick = (item) => {
    if (isSelectionMode.value) {
        // 选择模式下切换选中状态
        const isSelected = selectedItems.value.includes(item.vaId)
        handleItemSelect(item.vaId, !isSelected)
    }
    // 非选择模式下的点击由RecordCard组件自己处理
}

/**批量删除处理 */
const handleBatchDelete = async () => {
    if (selectedItems.value.length === 0) {
        proxy.$modal.msgWarning('请先选择要删除的项目')
        return
    }

    try {
        await proxy.$modal.confirm(
            `确定要删除选中的 ${selectedItems.value.length} 个音视频任务吗？此操作不可恢复！`,
            '批量删除确认',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
            }
        )

        // 执行批量删除
        loading.value = true
        await delTingwuTrans(selectedItems.value)

        proxy.$modal.msgSuccess(`成功删除 ${selectedItems.value.length} 个音视频任务`)

        // 清空选择并退出选择模式
        selectedItems.value = []
        isSelectionMode.value = false

        // 刷新列表
        await getList()

    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error)
            proxy.$modal.msgError('批量删除失败: ' + (error.message || '未知错误'))
        }
    } finally {
        loading.value = false
    }
}



// 状态映射函数
const mapTaskStatus = (apiStatus) => {
    const statusMap = {
        'ONGOING': '1',      // 转换中
        'COMPLETED': '2',    // 转换成功
        'FAILED': '3'        // 转换失败
    }
    return statusMap[apiStatus] || apiStatus
}

// 检查转换中任务的状态
const checkProcessingTasksStatus = async () => {
    try {
        isCheckingStatus.value = true
        // 筛选出状态为转换中(1)的任务
        const processingTasks = dataList.value.filter(item => item.vaStatus == '1' && item.taskId)

        if (processingTasks.length === 0) {
            console.log('当前没有转换中的任务')
            return
        }

        console.log(`检查 ${processingTasks.length} 个转换中的任务状态`)

        // 并发查询所有转换中任务的状态
        const statusPromises = processingTasks.map(
            async (task) => {
                try {
                    const response = await getStatusAndResultByTaskId(task.taskId)
                    return {
                        taskId: task.taskId,
                        vaId: task.vaId,
                        statusData: response.data
                    }
                } catch (error) {
                    console.error(`查询任务 ${task.taskId} 状态失败:`, error)
                    return null
                }
            })

        const results = await Promise.all(statusPromises)

        // 更新状态发生变化的任务
        for (const result of results) {
            if (!result) continue

            const { taskId, vaId, statusData } = result
            console.log(`任务 ${taskId} 状态数据:`, statusData)

            // 检查状态是否发生变化
            if (statusData && statusData.Data.TaskStatus !== undefined) {
                const currentTaskIndex = dataList.value.findIndex(item => item.vaId == vaId)
                const mappedStatus = mapTaskStatus(statusData.Data.TaskStatus)

                if (currentTaskIndex !== -1 && dataList.value[currentTaskIndex].vaStatus != mappedStatus) {
                    // 创建新的任务对象来触发响应式更新
                    const updatedTask = {
                        ...dataList.value[currentTaskIndex],
                        vaStatus: mappedStatus
                    }
                    if (updatedTask.vaStatus == '2') {
                        const res = await getTingwuTrans(updatedTask.vaId)
                        updatedTask.summary = res.data.summary
                        updatedTask.tags = res.data.tags
                        updatedTask.duration = res.data.duration

                    }
                    // 使用数组替换来触发响应式更新
                    dataList.value.splice(currentTaskIndex, 1, updatedTask)

                    console.log(`任务 ${taskId} 状态已更新到本地，新状态: ${mappedStatus}`)
                }
            }
        }

    } catch (error) {
        console.error('检查任务状态失败:', error)
    } finally {
        isCheckingStatus.value = false
    }
}

// 启动定时检查
const startStatusCheck = () => {
    console.log('启动定时状态检查，间隔3秒')

    // 立即执行一次检查
    checkProcessingTasksStatus()

    // 清除之前的定时器（如果存在）
    if (statusCheckTimer.value) {
        clearInterval(statusCheckTimer.value)
    }

    // 设置定时器，每10秒检查一次
    statusCheckTimer.value = setInterval(() => {
        console.log('定时器触发，开始检查状态...')
        checkProcessingTasksStatus()
    }, 10000) // 10秒间隔

    console.log('定时器已设置，ID:', statusCheckTimer.value)
}

// 停止定时检查
const stopStatusCheck = () => {
    if (statusCheckTimer.value) {
        console.log('停止定时状态检查，清除定时器ID', statusCheckTimer.value)
        clearInterval(statusCheckTimer.value)
        statusCheckTimer.value = null
        console.log('定时器已清除')
    } else {
        console.log('没有找到活动的定时器')
    }
}

onMounted(() => {
    getList().then(() => {
        // 数据加载完成后启动状态检查
        startStatusCheck()
    })
})

onUnmounted(() => {
    // 组件卸载时清理定时器
    stopStatusCheck()
})




</script>

<style lang="scss" scoped>
.record-page {
    background: #f7f9fc;
    min-height: 100vh;
    padding: 32px 0 0 0;
}
</style>
