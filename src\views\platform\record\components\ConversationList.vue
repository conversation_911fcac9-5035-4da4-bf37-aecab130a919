<template>
  <div class="original-section">
    <h3 class="section-title">原文</h3>
    <div class="conversation-list">
      <!-- 真实对话内容 -->
      <div
        v-for="(conversation, index) in formattedConversations"
        :key="index"
        class="conversation-item"
        :class="[
          `speaker-${conversation.speakerId}`,
          {
            'currently-playing': currentPlayingIndex === index,
            'search-result': isSearchResult(index),
            'current-search-result': isCurrentSearchResult(index),
            'editing': editingIndex === index,
            'clickable': editingIndex !== index
          }
        ]"
        @click="editingIndex !== index ? handleConversationClick(conversation) : null"
        :title="editingIndex !== index ? '点击跳转到音频位置: ' + formatTimestamp(conversation.startTime) : ''"
      >
        <div class="conversation-header">
          <div class="left-section">
            <div class="speaker-id-circle" :style="{ backgroundColor: getSpeakerColor(conversation.speakerId) }">
              {{ conversation.speakerId }}
            </div>
            <div class="timestamp">
              <span v-if="currentPlayingIndex === index" class="playing-indicator">▶</span>
              {{ formatTimestamp(conversation.startTime) }} - {{ formatTimestamp(conversation.endTime) }}
            </div>
          </div>
          <div class="header-right">
            <div class="speaker-name" :style="{ color: getSpeakerColor(conversation.speakerId) }">
              {{ conversation.speakerName }}：
            </div>
            <div class="action-buttons">
              <!-- 编辑按钮 -->
              <el-button
                v-if="editingIndex !== index"
                @click.stop="startEdit(index)"
                type="text"
                size="default"
                class="edit-btn"
                title="编辑文本"
              >
                <el-icon size="18"><Edit /></el-icon>
              </el-button>
              <!-- 保存和取消按钮 -->
              <div v-else class="edit-actions">
                <el-button
                  @click.stop="saveEdit(index)"
                  type="primary"
                  size="default"
                  class="save-btn"
                  title="保存修改"
                >
                  <el-icon size="16"><Check /></el-icon>
                </el-button>
                <el-button
                  @click.stop="cancelEdit()"
                  type="default"
                  size="default"
                  class="cancel-btn"
                  title="取消编辑"
                >
                  <el-icon size="16"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 显示模式 -->
        <p
          v-if="editingIndex !== index"
          class="conversation-text"
          v-html="renderTextWithWordHighlight(conversation)"
          @click="handleWordClickEvent"
        >
        </p>
        <!-- 编辑模式 -->
        <div v-else class="edit-container">
          <el-input
            v-model="editingText"
            type="textarea"
            :rows="3"
            placeholder="请输入文本内容..."
            class="edit-textarea"
            @keydown.ctrl.enter="saveEdit(index)"
            @keydown.esc="cancelEdit()"
            ref="editTextarea"
          />
          <div class="edit-tips">
            <span class="tip-text">Ctrl+Enter 保存，Esc 取消</span>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-if="formattedConversations.length === 0" class="no-data">
        <p>暂无对话内容</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, nextTick } from 'vue'
import { Edit, Check, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  paragraphs: {
    type: String,
    default: ''
  },
  currentPlayingIndex: {
    type: Number,
    default: -1
  },
  searchKeyword: {
    type: String,
    default: ''
  },
  searchResults: {
    type: Array,
    default: () => []
  },
  currentSearchIndex: {
    type: Number,
    default: -1
  },
  currentPlayingTime: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['conversation-click', 'text-updated'])

// 编辑相关状态
const editingIndex = ref(-1)
const editingText = ref('')
const editTextarea = ref(null)

// 格式化对话内容 - 处理新的数据格式
const formattedConversations = computed(() => {
  if (!props.paragraphs) return []
  try {
    const paragraphsData = JSON.parse(props.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      console.error('paragraphsData 不是数组格式:', typeof paragraphsData, paragraphsData)
      return []
    }

    return paragraphsData
  } catch (error) {
    console.error('解析对话内容失败:', error)
    return []
  }
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取说话人颜色
const getSpeakerColor = (speakerId) => {
  const colors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#fa8c16', // 橙色
    '#eb2f96', // 粉色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#f5222d', // 红色
    '#faad14', // 黄色
  ]
  const index = parseInt(speakerId) % colors.length
  return colors[index]
}

// 点击对话item跳转到对应音频位置
const handleConversationClick = (conversation) => {
  emit('conversation-click', conversation)
}

// 渲染带有词汇高亮的文本
const renderTextWithWordHighlight = (conversation) => {
  if (!conversation.wordDetails || !Array.isArray(conversation.wordDetails)) {
    // 如果没有词汇详情，使用原始文本并应用搜索高亮
    return highlightSearchKeyword(conversation.text, props.searchKeyword)
  }

  const currentTime = props.currentPlayingTime
  let html = ''

  // 动态调整时间容差，根据词汇长度和播放速度
  const baseTolerance = 50 // 基础容差50ms

  // 找到最匹配的当前词汇
  let currentWordIndex = -1
  let bestMatch = { index: -1, score: Infinity }

  const isInCurrentConversation = props.currentPlayingIndex !== -1 &&
    formattedConversations.value[props.currentPlayingIndex] === conversation

  if (isInCurrentConversation) {
    conversation.wordDetails.forEach((word, index) => {
      // 计算时间距离分数
      const startDistance = Math.abs(currentTime - word.startTime)
      const endDistance = Math.abs(currentTime - word.endTime)
      const minDistance = Math.min(startDistance, endDistance)

      // 如果当前时间在词汇时间范围内，优先选择
      if (currentTime >= word.startTime && currentTime <= word.endTime) {
        currentWordIndex = index
        return
      }

      // 否则选择距离最近的词汇（在容差范围内）
      const tolerance = baseTolerance + (word.endTime - word.startTime) * 0.1 // 根据词汇长度调整容差
      if (minDistance <= tolerance && minDistance < bestMatch.score) {
        bestMatch = { index, score: minDistance }
      }
    })

    // 如果没有完全匹配的词汇，使用最佳匹配
    if (currentWordIndex === -1 && bestMatch.index !== -1) {
      currentWordIndex = bestMatch.index
    }
  }

  conversation.wordDetails.forEach((word, index) => {
    let wordText = word.text
    let wordClass = 'word'

    // 如果是当前播放的词汇，使用高亮样式
    if (index === currentWordIndex) {
      wordClass = 'current-word'
      // 调试信息（减少日志输出）
      console.log(`当前高亮词汇: "${wordText}" (${word.startTime}-${word.endTime}ms), 当前时间: ${currentTime}ms, 匹配分数: ${bestMatch.score}`)
    }

    // 应用搜索关键词高亮（在词汇内部）
    if (props.searchKeyword && props.searchKeyword.trim()) {
      wordText = highlightSearchKeyword(wordText, props.searchKeyword)
    }

    // 添加点击事件处理，使词汇可以跳转到对应音频位置
    const timeTooltip = `点击跳转到 ${formatTimestamp(word.startTime)}`
    html += `<span class="${wordClass} clickable-word" data-start="${word.startTime}" data-end="${word.endTime}" title="${timeTooltip}">${wordText}</span>`
  })

  return html
}

// 高亮搜索关键词
const highlightSearchKeyword = (text, keyword) => {
  if (!keyword || !keyword.trim()) {
    return text
  }

  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}



// 检查当前对话是否是搜索结果
const isSearchResult = (index) => {
  return props.searchResults.some(result => result.conversationIndex === index)
}

// 检查当前对话是否是当前搜索结果
const isCurrentSearchResult = (index) => {
  if (props.currentSearchIndex === -1 || !props.searchResults.length) return false
  const currentResult = props.searchResults[props.currentSearchIndex]
  return currentResult && currentResult.conversationIndex === index
}

// 编辑相关方法
const startEdit = (index) => {
  // 验证索引和数据有效性
  if (index < 0 || index >= formattedConversations.value.length) {
    console.error('编辑索引无效:', index, '总数:', formattedConversations.value.length)
    ElMessage.error('编辑失败：数据索引无效')
    return
  }

  const conversation = formattedConversations.value[index]
  if (!conversation || !conversation.hasOwnProperty('text')) {
    console.error('对话数据无效:', conversation)
    ElMessage.error('编辑失败：对话数据无效')
    return
  }

  editingIndex.value = index
  editingText.value = conversation.text

  // 聚焦到文本框
  nextTick(() => {
    if (editTextarea.value) {
      editTextarea.value.focus()
    }
  })
}

const saveEdit = async (index) => {
  if (!editingText.value.trim()) {
    ElMessage.warning('文本内容不能为空')
    return
  }

  // 验证索引和数据有效性
  if (index < 0 || index >= formattedConversations.value.length) {
    console.error('保存索引无效:', index, '总数:', formattedConversations.value.length)
    ElMessage.error('保存失败：数据索引无效')
    return
  }

  const conversation = formattedConversations.value[index]
  if (!conversation) {
    console.error('对话数据无效:', conversation)
    ElMessage.error('保存失败：对话数据无效')
    return
  }



  try {
    // 显示保存中状态
    const loadingMessage = ElMessage({
      message: '正在保存...',
      type: 'info',
      duration: 0
    })

    // 发送更新事件给父组件并等待完成
    await new Promise((resolve, reject) => {
      emit('text-updated', {
        index,
        newText: editingText.value.trim(),
        conversation: conversation,
        resolve,
        reject
      })
    })

    // 关闭加载提示
    loadingMessage.close()

    // 退出编辑模式
    cancelEdit()

    ElMessage.success('文本修改并保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const cancelEdit = () => {
  editingIndex.value = -1
  editingText.value = ''
}

// 处理词汇点击事件
const handleWordClickEvent = (event) => {
  // 检查点击的是否是词汇元素或其子元素（如搜索高亮的mark标签）
  let target = event.target

  // 如果点击的是搜索高亮标签，向上查找词汇元素
  if (target.tagName === 'MARK' && target.classList.contains('search-highlight')) {
    target = target.parentElement
  }

  if (target && target.classList.contains('clickable-word')) {
    const startTime = parseInt(target.getAttribute('data-start'))
    const endTime = parseInt(target.getAttribute('data-end'))

    if (!isNaN(startTime)) {
      console.log(`点击词汇跳转到音频位置: ${startTime}ms - ${endTime}ms`)

      // 创建一个模拟的对话对象来触发跳转
      const mockConversation = {
        startTime: startTime,
        endTime: endTime,
        text: target.textContent || target.innerText
      }

      // 触发对话点击事件，跳转到音频位置
      emit('conversation-click', mockConversation)

      // 阻止事件冒泡，避免触发对话项的点击事件
      event.stopPropagation()
    }
  }
}
</script>

<style lang="scss" scoped>
// 通用变量定义
$primary-color: #3b82f6;
$primary-dark: #1d4ed8;
$primary-light: #f0f9ff;
$primary-lighter: #e0f2fe;
$border-color: #e2e8f0;
$text-color: #334155;
$text-muted: #64748b;
$text-light: #9ca3af;
$shadow-light: rgba(0, 0, 0, 0.08);
$shadow-medium: rgba(0, 0, 0, 0.12);
$shadow-heavy: rgba(0, 0, 0, 0.15);
$transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s ease;

// 通用混合器
@mixin gradient-bg($color1, $color2) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
}

@mixin shadow-hover($shadow) {
  box-shadow: $shadow;
  transform: translateY(-2px);
}

@mixin button-base {
  padding: 6px 8px;
  min-width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: $transition-fast;
}

@mixin pulse-animation($name, $shadow1, $shadow2) {
  animation: #{$name} 2s ease-in-out infinite;

  @keyframes #{$name} {
    0%, 100% { box-shadow: $shadow1; }
    50% { box-shadow: $shadow2; }
  }
}

// 说话人颜色配置
$speaker-colors: (
  1: (#1890ff, #096dd9),
  2: (#52c41a, #389e0d),
  3: (#fa8c16, #d46b08),
  4: (#eb2f96, #c41d7f),
  5: (#722ed1, #531dab),
  6: (#13c2c2, #08979c),
  7: (#f5222d, #cf1322),
  8: (#faad14, #d48806)
);

/* 原文对话区域样式 */
.original-section {
  margin: 0;
  padding-top: 0;
  border-top: none;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 24px 0;
  position: relative;
  padding-left: 16px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    @include gradient-bg($primary-color, $primary-dark);
    border-radius: 2px;
  }
}

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 120px;
}

.conversation-item {
  @include gradient-bg(#ffffff, #fafbfc);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px $shadow-light;
  border-left: 4px solid $border-color;
  border: 1px solid rgba($border-color, 0.5);
  transition: $transition-smooth;
  position: relative;

  &:hover {
    @include shadow-hover(0 8px 32px $shadow-medium);
    border-color: rgba($primary-color, 0.3);
  }

  &.clickable {
    cursor: pointer;
    user-select: none;

    &:hover {
      @include shadow-hover(0 12px 40px $shadow-heavy);
      border-color: rgba($primary-color, 0.5);
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    }
  }

  &.currently-playing {
    @include gradient-bg($primary-light, $primary-lighter);
    border-left-color: $primary-color !important;
    border-color: rgba($primary-color, 0.4);
    @include pulse-animation(
      currentlyPlayingPulse,
      0 8px 32px rgba($primary-color, 0.2) 0 0 0 2px rgba($primary-color, 0.1),
      0 12px 40px rgba($primary-color, 0.3) 0 0 0 3px rgba($primary-color, 0.2)
    );

    &::before {
      content: '🎵';
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 16px;
      animation: musicNote 1.5s ease-in-out infinite;
    }
  }
}

@keyframes musicNote {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes speakerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  }
}

@keyframes speakerHalo {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
}

/* 不同说话人的边框颜色和编号样式 */
@each $id, $colors in $speaker-colors {
  $primary: nth($colors, 1);
  $dark: nth($colors, 2);

  .conversation-item.speaker-#{$id} {
    border-left-color: $primary;

    .speaker-id-circle {
      background-color: $primary !important;
      box-shadow: 0 3px 8px rgba($primary, 0.4);

      &::before {
        background: $primary;
      }
    }
  }
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .conversation-item:hover &,
  .conversation-item.editing & {
    opacity: 1;
  }
}

.edit-btn {
  @include button-base;
  color: #909399;

  &:hover {
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
  }
}

.edit-actions {
  display: flex;
  gap: 4px;
}

.save-btn, .cancel-btn {
  @include button-base;
}

.save-btn {
  background: #67c23a;
  border-color: #67c23a;
  color: white;

  &:hover {
    background: #5daf34;
    border-color: #5daf34;
  }
}

.cancel-btn {
  background: #f4f4f5;
  border-color: #dcdfe6;
  color: #909399;

  &:hover {
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}

.timestamp {
  font-size: 11px;
  color: $text-muted;
  @include gradient-bg(#f1f5f9, $border-color);
  padding: 4px 12px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  border: 1px solid rgba($border-color, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 6px;
}

.playing-indicator {
  color: $primary-color;
  font-size: 10px;
  animation: playingBlink 1.5s ease-in-out infinite;
}

@keyframes playingBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.speaker-id-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  color: white;
  font-size: 16px;
  line-height: 1;
  font-weight: 800;
  border-radius: 50%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.9);
  flex-shrink: 0;
  transition: $transition-smooth;
  animation: speakerPulse 3s ease-in-out infinite;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    z-index: -1;
    animation: speakerHalo 3s ease-in-out infinite;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation-play-state: paused;
  }
}

.speaker-name {
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.02em;
  transition: $transition-fast;

  &:hover {
    transform: translateY(-1px);
  }
}

.conversation-text {
  color: $text-color;
  line-height: 1.8;
  margin: 0;
  font-size: 15px;
  text-align: justify;
  font-weight: 400;
  letter-spacing: 0.01em;
}

/* 编辑相关样式 */
.conversation-item.editing {
  border-color: $primary-color;
  @include gradient-bg($primary-light, $primary-lighter);
  box-shadow: 0 8px 32px rgba($primary-color, 0.2);
}

.edit-container {
  margin-top: 8px;
}

.edit-textarea {
  margin-bottom: 8px;

  :deep(.el-textarea__inner) {
    border: 2px solid $primary-color;
    border-radius: 8px;
    font-size: 15px;
    line-height: 1.6;
    resize: vertical;
    min-height: 80px;

    &:focus {
      border-color: $primary-dark;
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
    }
  }
}

.edit-tips {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.tip-text {
  font-size: 12px;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: $text-light;

  p {
    margin: 0;
    font-size: 14px;
  }
}

/* 搜索相关样式 */
.conversation-item.search-result {
  border: 2px solid rgba($primary-color, 0.3);
  @include gradient-bg($primary-light, $primary-lighter);
}

.conversation-item.current-search-result {
  border: 2px solid $primary-color;
  @include gradient-bg(#dbeafe, #bfdbfe);
  @include pulse-animation(
    searchResultPulse,
    0 8px 32px rgba($primary-color, 0.3) 0 0 0 3px rgba($primary-color, 0.1),
    0 12px 40px rgba($primary-color, 0.4) 0 0 0 4px rgba($primary-color, 0.2)
  );
}

.conversation-text :deep(.search-highlight) {
  @include gradient-bg(#fef08a, #fde047);
  color: #92400e;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: highlightGlow 1.5s ease-in-out infinite alternate;
}

@keyframes highlightGlow {
  from { @include gradient-bg(#fef08a, #fde047); }
  to { @include gradient-bg(#fbbf24, #f59e0b); }
}

/* 词汇高亮样式 */
.conversation-text :deep(.word) {
  transition: $transition-fast;
  border-radius: 3px;
  padding: 1px 2px;
  display: inline-block;
  position: relative;
}

.conversation-text :deep(.clickable-word) {
  cursor: pointer;
  transition: $transition-fast;
  border-bottom: 1px dotted transparent;
  position: relative;

  .conversation-item.currently-playing & {
    border-bottom: 1px dotted rgba($primary-color, 0.4);

    &:hover {
      border-bottom: 1px dotted $primary-color;
    }
  }

  &:hover {
    background: rgba($primary-color, 0.15);
    color: $primary-color;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba($primary-color, 0.2);
    border-bottom: 1px dotted $primary-color;
  }

  &:active {
    background: rgba($primary-color, 0.25);
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba($primary-color, 0.3);
  }
}

.conversation-text :deep(.word:hover) {
  background: rgba($primary-color, 0.15);
  color: $primary-color;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba($primary-color, 0.2);
  border-bottom: 1px dotted $primary-color;
}

.conversation-text :deep(.current-word) {
  @include gradient-bg($primary-color, $primary-dark);
  color: white;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 600;
  box-shadow:
    0 2px 8px rgba($primary-color, 0.4),
    0 0 0 2px rgba($primary-color, 0.2);
  animation: currentWordGlow 2s ease-in-out infinite;
  transition: $transition-fast;
  transform: translateY(-1px) scale(1.02);
  z-index: 1;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    @include gradient-bg($primary-color, $primary-dark);
    border-radius: 8px;
    opacity: 0.3;
    z-index: -1;
    animation: currentWordHalo 2s ease-in-out infinite;
  }
}

@keyframes currentWordGlow {
  0%, 100% {
    box-shadow:
      0 2px 8px rgba($primary-color, 0.4),
      0 0 0 2px rgba($primary-color, 0.2);
  }
  50% {
    box-shadow:
      0 4px 16px rgba($primary-color, 0.6),
      0 0 0 3px rgba($primary-color, 0.3);
  }
}

@keyframes currentWordHalo {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
}
</style>
