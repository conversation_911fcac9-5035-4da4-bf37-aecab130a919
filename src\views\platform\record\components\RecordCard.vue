<template>
  <div class="record-card" @click="toPage">
    <div class="record-card-header">
      <span class="record-title">{{ record.vaName }}</span>
      <span v-if="record.vaStatus" class="record-dot" :class="getStatusClass(record.vaStatus)">{{ formattedStatus }}</span>
    </div>
    
    <!--全文概要 -->
    <div class="record-card-content" v-if="record.tags && record.summary">
      <div class="record-card-tags">
        <el-tag v-for="(tag, idx) in formattedTags" :key="idx" size="small" class="record-tag">
          {{ tag }}
        </el-tag>
      </div>
      <div class="record-source-text  summary-truncate">{{ record.summary }}</div>
    </div>
    <div class="record-card-content" v-else>
      <el-empty description="这里空空如也" />
    </div>
    <div class="record-card-footer">
      <span class="record-time">
        <el-icon><Timer /></el-icon>
        {{ formatTime(Math.floor(record.duration / 1000)) }}
      </span>
      <span class="record-date">{{ record.createTime }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'




const props = defineProps({
  record: {
    type: Object,
    required: true
  },
  selectionMode: {
    type: Boolean,
    default: false
  },
  viewMode: {
    type: String,
    default: 'grid' // 'grid' 或 'list'
  }
})

const router = useRouter()

// 将字符串标签转换为数组
const formattedTags = computed(() => {
  if (typeof props.record.tags === 'string') {
    return props.record.tags.replace(/[\[\]\"]/g, '').split(',').filter(tag => tag.trim() !== '')
  }
  return Array.isArray(props.record.tags) ? props.record.tags : []
})


// 格式化音视频状态
const formattedStatus = computed(() => {
  const statusMap = {
    '1': '转换中',
    '2': '转换成功',
    '3': '转换失败'
  }
  return statusMap[props.record.vaStatus] || '未知状态'
})

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    1: 'status-processing', // 转换中 - 蓝色
    2: 'status-success',    // 转换成功 - 绿色
    3: 'status-error'       // 转换失败 - 红色
  }
  return classMap[status] || 'status-unknown'
}

const formatTime = (seconds) => {
  // 处理无效值
  if (!seconds || isNaN(seconds) || seconds < 0) {
    return '00:00'
  }

  // 处理超长时间（超过1小时）
  if (seconds >= 3600) {
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 标准格式（分:秒）
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
const toPage = () => {
  // 如果处于选择模式，不执行跳转
  if (props.selectionMode) {
    console.log('In selection mode, not navigating')
    return
  }

  console.log('Navigating to record detail page:', props.record.vaId)
  // 跳转到记录详情页
  router.push({
    name: 'recordDetail',
    params: { vaId: props.record.vaId }
  })
}
</script>

<style lang="scss" scoped>
.record-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
  height: 340px;

  // 添加微妙的渐变边框效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: rgba(59, 130, 246, 0.2);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-1px);
    transition: all 0.1s ease;
  }

  .record-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 12px;

    .record-title {
      font-weight: 600;
      font-size: 16px;
      color: #1e293b;
      line-height: 1.4;
      flex: 1;
      word-break: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s ease;

      &:hover {
        color: #3b82f6;
      }
    }

    .record-dot {
      font-size: 11px;
      padding: 6px 12px;
      border-radius: 20px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      // 转换中 - 蓝色渐变
      &.status-processing {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: shimmer 2s infinite;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
      }

      // 转换成功 - 绿色渐变
      &.status-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }
      }

      // 转换失败 - 红色渐变
      &.status-error {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }
      }

      // 未知状态 - 灰色渐变
      &.status-unknown {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
        }
      }
    }
  }

  // 闪烁动画
  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  .record-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    max-height: calc(3 * (20px + 6px));
    overflow: hidden;
    margin-bottom: 12px;

    .record-tag {
      margin: 0;
      flex-shrink: 0;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
      color: #3b82f6;
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      padding: 4px 8px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
      }
    }
  }

  .record-card-content {
    flex: 1;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 220px;

    .record-cover {
      width: 120px;
      height: 80px;
      object-fit: contain;
      border-radius: 6px;
      background: #f5f7fa;
      margin-bottom: 8px;
    }

    .record-empty {
      width: 120px;
      height: 80px;
      background: #f5f7fa;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #bbb;
      font-size: 13px;
      margin-bottom: 8px;
    }

    .record-source-text {
      color: #666;
      font-size: 13px;
      margin-top: 4px;
      text-align: left;
      width: 100%;
      word-break: break-all;
      margin-top: 6px;
      line-height: 1.7;

      &.summary-truncate {
        display: -webkit-box;
        -webkit-line-clamp: 5;
        line-clamp: 5;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .record-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(226, 232, 240, 0.6);

    .record-time {
      color: #64748b;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;

      // &::before {
      //   content: '⏱';
      //   font-size: 12px;
      // }

      &:hover {
        background: rgba(59, 130, 246, 0.15);
        color: #3b82f6;
      }
    }

    .record-date {
      color: #94a3b8;
      font-size: 12px;
      font-weight: 400;
    }
  }
}

// 空状态优化
:deep(.el-empty__image svg) {
  width: 80px;
  height: 80px;
  opacity: 0.6;
}

:deep(.el-empty__description) {
  color: #94a3b8;
  font-size: 13px;
  margin-top: 8px;
}
</style>
