<script setup lang="ts" name="VideoEdit">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { VideoPlay, InfoFilled } from '@element-plus/icons-vue'; // 添加图标导入

// 组件
import TimelineComponent from './components/Timeline.vue';
import LeftPanel from './components/LeftPanel.vue';
import TimelinePlayer from './components/TimelinePlayer.vue';
import HeaderToolbar from './components/HeaderToolbar.vue';
import Levelmeter from './components/Levelmeter.vue';
import SaveAsTemplateDialog from './components/SaveAsTemplateDialog.vue';

// API & 工具
import { getEditingProject } from '../api/videoEdit';
import { getTemplate } from '../api/template';
import { convertTemplateToTimeline, enrichTimelineWithMediaDetails } from '../utils/timeUtils';
import { useVideoEditorStore } from './useVideoEditor';

// 类型
import type { VideoEditProject, Timeline } from '../types/videoEdit';
import type { TemplateInfo } from '../types/template';

// 开发环境调试
// if (import.meta.env.DEV) {
//   import('./debug/templateDataDebug').then(({ testCompleteFixedSystem }) => {
//     (window as any).debugTemplateData = testCompleteFixedSystem;
//     console.log('🔧 调试工具已加载: debugTemplateData()');
//   });
// }

// ========== 初始化 ==========
const videoEditorStore = useVideoEditorStore();
const route = useRoute();
const router = useRouter();

type EditSource = 'cloud-editing' | 'template-factory';

// ========== 状态管理 ==========
const loading = ref(false);
const editSource = ref<EditSource>('cloud-editing');
const projectDetails = ref<Partial<VideoEditProject & TemplateInfo> | null>(null);
const activeVideoForMeter = ref<HTMLVideoElement | null>(null);
const timelinePlayerRef = ref(null);
const videoSource = ref('');
const producedMediaId = ref<string | null>(null);
const showSaveAsTemplateDialog = ref(false);
const showLeftPanel = ref(true);

// ========== 方法 ==========
const goBack = () => router.back();
const toggleLeftPanel = () => showLeftPanel.value = !showLeftPanel.value;

// 处理活跃视频变化
const handleActiveVideoChange = (videoEl: HTMLVideoElement | null) => {
  // Canvas 渲染引擎模式下，此回调可能不会触发
  // 因为视频是渲染到 Canvas 上的，而不是直接的 video 元素
  activeVideoForMeter.value = videoEl;
};

// 处理添加元素
const handleAddElement = (element: any) => {
  console.log("Element added to timeline:", element);
};

// 保存为模板
const handleSaveAsTemplate = () => {
  if (!videoEditorStore.timeline) {
    ElMessage.warning("没有可用的时间线数据");
    return;
  }
  
  // 获取播放校验信息
  const playbackInfo = videoEditorStore.getPlaybackValidationInfo();
  console.log('🎯 当前播放校验信息:', playbackInfo);
  
  showSaveAsTemplateDialog.value = true;
};

// 保存项目
const handleSave = () => {
  console.log("触发保存操作");
  ElMessage.info("保存功能待实现");
};

// 处理导出
const handleExportCommand = (command: string) => {
  const actions: Record<string, string> = {
    'export-clips': '各片段独立导出',
    'export-composition': '片段合成导出',
    'export-video': '导出视频'
  };
  
  const actionName = actions[command] || '未知操作';
  console.log(`触发${actionName}`);
  ElMessage.info(`${actionName}功能待实现`);
};

// 加载项目或模板
async function loadProjectOrTemplate(id: string) {
  loading.value = true;
  try {
    let rawTimeline: Timeline | null = null;

    if (editSource.value === 'template-factory') {
      // 加载模板
      const response = await getTemplate(id, { relatedMediaidFlag: '1' });
      const template = response.data.Template;

      videoEditorStore.setTemplateDetailData(template);
      projectDetails.value = {
        ProjectId: template.TemplateId,
        Title: template.Name,
        Status: template.Status,
      };
      videoEditorStore.setEditingTemplateInfo(template.TemplateId, template.Name);
      videoEditorStore.setEditingProjectId('', template.TemplateId);

      if (template.Config) {
        rawTimeline = convertTemplateToTimeline(template);
        
        // 添加详细的调试信息
        console.log('📋 模板详细信息:', {
          templateId: template.TemplateId,
          name: template.Name,
          hasConfig: !!template.Config,
          clipsParam: template.ClipsParam,
          clipsParamEmpty: !template.ClipsParam || template.ClipsParam.trim() === '{}',
          relatedMediaids: template.RelatedMediaids
        });
        
        const parsedMaterials = videoEditorStore.parseTemplateConfigForMaterials(template);
        videoEditorStore.setTemplateMaterials(parsedMaterials);
        console.log('解析到的模板可变素材:', parsedMaterials);
        
        if (parsedMaterials.length === 0) {
          console.warn('⚠️ 没有解析到任何可变素材，可能原因：');
          console.warn('1. ClipsParam为空且时间轴片段没有IsFromTemplate标记');
          console.warn('2. 时间轴片段缺少必要的MediaId或Title字段');
          console.warn('3. 智能检测逻辑可能需要调整');
        }
      } else {
        ElMessage.warning("模板没有有效配置");
      }
    } else {
      // 加载云剪辑项目
      const response = await getEditingProject(id);
      projectDetails.value = response.data.Project;
      const timelineJson = projectDetails.value?.Timeline;

      videoEditorStore.setEditingTemplateInfo('', '');

      if (!projectDetails.value.ProjectId) {
        console.error('⚠️ 工程详情接口未返回ProjectId');
        ElMessage.error('工程详情接口异常');
        return;
      }
      
      videoEditorStore.setEditingProjectId(projectDetails.value.ProjectId, '');

      if (projectDetails.value?.CoverURL) {
        videoSource.value = projectDetails.value.CoverURL;
        producedMediaId.value = projectDetails.value?.ProduceMediaId || null;
        console.log('检测到已生成的 CoverURL:', videoSource.value);
      } else if (timelineJson) {
        rawTimeline = JSON.parse(timelineJson);
      }
    }

    if (rawTimeline) {
      videoEditorStore.setTimeline(await enrichTimelineWithMediaDetails(rawTimeline));
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "加载失败";
    console.error("加载项目或模板失败:", error);
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
}

// ========== 缩放控制 ==========
const handleWheel = (event: WheelEvent) => {
  if (event.ctrlKey) event.preventDefault();
};

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey && ['+', '-', '0'].includes(event.key)) {
    event.preventDefault();
  }
};

// ========== 引擎重新初始化处理 ==========
const handleEngineInitialized = (data: { engine: any, playerInstance: any }) => {
  console.log('🔄 Canvas渲染引擎重新初始化，重新连接音频电平监控');
  // 重新设置播放器引用，这将重新连接音频电平监控
  videoEditorStore.setPlayerRef(data.playerInstance);
};

// ========== 生命周期 ==========
onMounted(() => {
  // 绑定播放器引用
  watch(timelinePlayerRef, (newRef) => {
    videoEditorStore.setPlayerRef(newRef);
  }, { immediate: true });

  // 设置编辑来源
  if (route.query.from === 'template-factory') {
    editSource.value = 'template-factory';
    videoEditorStore.setEditSource('template-factory');
  } else {
    videoEditorStore.setEditSource('cloud-editing');
  }

  // 加载项目
  const id = route.params.projectId as string;
  if (id) {
    loadProjectOrTemplate(id).then(() => {
      // Canvas 渲染引擎会自动处理媒体资源加载
      console.log('🎨 使用 Canvas 渲染引擎模式');
    });
  } else {
    ElMessage.error('未找到项目ID');
    goBack();
  }

  // 禁用页面缩放
  window.addEventListener('wheel', handleWheel, { passive: false });
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  window.removeEventListener('wheel', handleWheel);
  window.removeEventListener('keydown', handleKeyDown);
});
</script>

<template>
  <div class="vid-edit-wrapper">
    <!-- 顶部工具栏 -->
    <HeaderToolbar 
      :title="projectDetails?.Title || '智能媒体生产'" 
      :edit-source="editSource" 
      @go-back="goBack"
      @save-as-template="handleSaveAsTemplate" 
      @save="handleSave" 
      @export="handleExportCommand" 
    />

    <!-- 主内容区 -->
    <div class="vid-edit-view-wrap">
      <div class="vid-edit-top-content-row"> <!-- 修复类名 -->
        <!-- 左侧面板 -->
        <div :class="['left-panel-container', { 'is-collapsed': !showLeftPanel }]">
          <LeftPanel 
            @add-element="handleAddElement" 
            :materials="videoEditorStore.templateMaterials"
            :edit-source="editSource" 
            :is-collapsed="!showLeftPanel" 
            @toggle-collapse="toggleLeftPanel" 
          />
        </div>

        <!-- 中心播放器 -->
        <div class="vid-edit-center-panel">
          <div class="vid-edit-video-player-wrapper">
            <TimelinePlayer 
              ref="timelinePlayerRef" 
              :source="videoSource" 
              :timeline="videoEditorStore.timeline"
              @update:is-playing="videoEditorStore.updateIsPlaying"
              @update:current-time="videoEditorStore.updateCurrentTime"
              @update:duration="videoEditorStore.updateDuration"
              @active-video-element-changed="handleActiveVideoChange" 
              @engine-initialized="handleEngineInitialized"
            />
          </div>
          
          <!-- 播放控制条 -->
          <div class="vid-edit-player-controls">
            <span>{{ videoEditorStore.currentTimeFormatted }} / {{ videoEditorStore.videoDurationFormatted }}</span>
            <el-button type="primary" :loading="loading">
              <el-icon><VideoPlay /></el-icon>
              生成视频
            </el-button>
          </div>
        </div>

        <!-- 右侧音频电平表 -->
        <div class="vid-edit-right-panel">
          <Levelmeter 
            :enabled="true"
          />
          <!-- 音频监控状态提示 -->
          <div v-if="videoEditorStore.audioLevelUpdateEnabled" class="audio-monitoring-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>Canvas 音频监控中</span>
          </div>
          <div v-else class="audio-monitoring-tip disabled">
            <el-icon><InfoFilled /></el-icon>
            <span>音频监控已禁用</span>
          </div>
        </div>
      </div>

      <!-- 底部时间轴 -->
      <div class="vid-edit-bottom-timeline-row">
        <TimelineComponent :timeline="videoEditorStore.timeline" />
      </div>
    </div>

    <!-- 另存为模板弹窗 -->
    <SaveAsTemplateDialog 
      v-model:visible="showSaveAsTemplateDialog" 
      :timeline="videoEditorStore.timeline"
      :preview-media-id="producedMediaId" 
    />

    <!-- 加载遮罩 -->
    <div 
      v-loading="loading" 
      element-loading-text="正在加载..." 
      element-loading-background="rgba(0, 0, 0, 0.7)"
      class="loading-overlay"
    ></div>
  </div>
</template>

<style lang="scss" scoped>
.vid-edit-wrapper {
  background-color: #1a1e22;
  color: #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.vid-edit-view-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 10px;
  overflow: hidden;
  min-height: 0;
  height: 50vh;
}

.vid-edit-top-content-row { // 修复类名
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.left-panel-container {
  transition: width 0.3s ease;
  height: 50vh;

  &.is-collapsed {
    width: 80px;
  }
}

.vid-edit-center-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;

  .vid-edit-video-player-wrapper {
    flex-grow: 1;
    background-color: #1a1e22;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 0;
  }

  .vid-edit-player-controls {
    flex-shrink: 0;
    background-color: #24292e;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #a8b2c2;
  }
}

.vid-edit-right-panel {
  flex-shrink: 0;
  width: 150px;
  display: flex;
  flex-direction: column;
  
  .canvas-mode-tip {
    margin-top: 10px;
    padding: 8px;
    background-color: #2a2d2e;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #888;
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.vid-edit-bottom-timeline-row {
  height: 40vh;
  flex-shrink: 0;
  background-color: #24292e;
}
</style>