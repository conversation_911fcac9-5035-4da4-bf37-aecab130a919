<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>云剪辑</span>
        </div>
      </template>
      <el-tabs v-model="activeTab" @tab-click="handleQuery">
        <el-tab-pane label="视频剪辑工程" name="video"></el-tab-pane>
        <el-tab-pane label="直播剪辑工程" name="live"></el-tab-pane>
      </el-tabs>
      
      <div class="table-toolbar">
        <div class="toolbar-left-group">
          <el-button icon="Plus" plain type="primary" @click="handleCreate">创建剪辑工程</el-button>
          <el-button :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()">删除</el-button>
          <el-input v-model="queryParams.keyword" placeholder="请输入工程标题/描述" style="width: 200px;" clearable
            @keyup.enter="handleQuery"></el-input>
          <el-select v-model="queryParams.status" placeholder="请选择状态" style="width: 180px;" clearable @change="handleQuery">
            <el-option label="全部" :value="undefined" />
            <el-option label="草稿(Draft)" value="Draft" />
            <el-option label="正常(Normal)" value="Normal" />
            <el-option label="已产出(Produced)" value="Produced" />
          </el-select>
          <el-button icon="Search" type="primary" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
        <div class="toolbar-right-group">
          <el-button plain>查看剪辑任务</el-button>
        </div>
      </div>

      <el-table v-loading="loading" :data="projectList" style="width: 100%; margin-top: 20px;"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="工程" width="130" align="center">
          <template #default="{ row }">
            <div class="project-info">
              <el-image class="project-thumbnail" :src="row.CoverURL" fit="cover">
                <template #error>
                  <div class="image-slot">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="#c0c4cc">
                      <path
                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                    </svg>
                  </div>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="工程ID/名称" width="250">
          <template #default="{ row }">
            <div class="project-id" :title="row.ProjectId">{{ row.ProjectId }}</div>
            <div class="project-title">{{ row.Title }}</div>
            <div v-if="row.Description" class="project-desc">{{ row.Description }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" width="130">
          <template #default="{ row }">
            <el-tag :type="row.Status === 'Normal' || row.Status === 'Produced' ? 'success' : 'warning'">{{ row.Status
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="CreateTime" width="200">
          <template #default="scope">
            <div class="time-info">
              <el-icon><Clock /></el-icon>
              <span>{{ parseTime(scope.row.CreateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="更新时间" prop="ModifiedTime" width="200">
          <template #default="scope">
            <div class="time-info">
              <el-icon><Clock /></el-icon>
              <span>{{ parseTime(scope.row.ModifiedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="340" fixed="right">
          <template #default="{ row }">
            <el-button icon="Edit" link type="primary" class="action-btn" @click="handleEdit(row)">剪辑</el-button>
            <el-button icon="Download" link type="primary" class="action-btn">导出模板</el-button>
            <el-button icon="Delete" link type="primary" @click="handleDelete(row)" class="action-btn">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加/修改剪辑工程对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px">
      <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工程标题" prop="Title">
          <el-input v-model="form.Title" placeholder="请输入工程标题" />
        </el-form-item>
        <el-form-item label="工程描述" prop="Description">
          <el-input v-model="form.Description" type="textarea" placeholder="请输入工程描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="VideoEditList">
import { ref, reactive, onMounted, toRefs, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { listEditingProjects, deleteEditingProjects, createEditingProject } from '../api/videoEdit';
import type { VideoEditProject, VideoEditListParams, CreateEditingProjectDTO } from '../types/videoEdit';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';

defineOptions({
  name: "VideoEditList"
});

const router = useRouter();
const activeTab = ref('video');
const projectFormRef = ref<FormInstance>();

const state = reactive({
  loading: true,
  ids: [] as string[],
  multiple: true,
  total: 0,
  projectList: [] as VideoEditProject[],
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    keyword: '',
    status: undefined,
  } as VideoEditListParams,
  form: {} as CreateEditingProjectDTO,
  dialog: {
    visible: false,
    title: ''
  },
  rules: {
    Title: [
      { required: true, message: "工程标题不能为空", trigger: "blur" }
    ],
  } as FormRules,
});

const { queryParams, loading, projectList, total, multiple, form, dialog, rules } = toRefs(state);

/** 取消按钮 */
function cancel() {
  state.dialog.visible = false;
  resetForm();
}

/** 表单重置 */
function resetForm() {
  state.form = {
    Title: '',
    Description: ''
  };
  projectFormRef.value?.resetFields();
}

/** 新增按钮操作 */
function handleCreate() {
  resetForm();
  state.dialog.visible = true;
  state.dialog.title = "创建剪辑工程";
}

/** 编辑按钮操作 */
function handleEdit(row: VideoEditProject) {
  router.push({
    path: `/tool/matrix-mix/video-edit/${row.ProjectId}`,
    query: { from: 'cloud-editing' }
  });
}

/** 提交按钮 */
async function submitForm() {
  projectFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await createEditingProject(state.form);
        ElMessage.success('创建成功');
        state.dialog.visible = false;
        getList();
      } catch (error) {
        console.error("创建失败: ", error);
        ElMessage.error("创建失败");
      }
    }
  });
}

/** 查询剪辑工程列表 */
async function getList() {
  state.loading = true;
  try {
    const response = await listEditingProjects(state.queryParams);
    // 前端分页处理
    const allProjects = response.data.ProjectList || [];
    state.total = response.data.TotalCount || allProjects.length;
    const { pageNum, pageSize } = state.queryParams;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    state.projectList = allProjects.slice(start, end);
  } catch (error) {
    ElMessage.error('获取剪辑工程列表失败');
    console.error(error);
  } finally {
    state.loading = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  state.queryParams.pageNum = 1;
  state.queryParams.keyword = '';
  state.queryParams.status = undefined; // Reset status filter
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: VideoEditProject[]) {
  state.ids = selection.map(item => item.ProjectId);
  state.multiple = !selection.length;
}

/** 删除按钮操作 */
async function handleDelete(row?: VideoEditProject) {
  const projectIds = row ? [row.ProjectId] : state.ids;
  if (projectIds.length === 0) {
    ElMessage.warning('请选择要删除的工程');
    return;
  }
  try {
    await ElMessageBox.confirm(
      `是否确认删除工程ID为 "${projectIds.join(', ')}" 的数据项？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    await deleteEditingProjects(projectIds.join(','));
    ElMessage.success('删除成功');
    getList(); // Refresh list
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

onMounted(() => {
  getList();
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 24px 32px;
  background: #f6f8fa;
  min-height: 100vh;

  .el-card {
    border-radius: 14px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.04);
    border: none;
    margin-bottom: 24px;
    padding-bottom: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 4px;
    border-bottom: 1px solid #f0f0f0;

    span {
      font-size: 22px;
      font-weight: 600;
      color: #222;
    }
    .button {
      color: #409eff;
      font-size: 14px;
      font-weight: 500;
      padding: 0 8px;
    }
  }

  .el-alert {
    margin-top: 18px;
    border-radius: 8px;
    font-size: 14px;
    background: #f4f8ff;
    border: 1px solid #e6f0fa;
    color: #3a4a5a;
    p {
      margin: 4px 0;
      color: #5c6b77;
    }
  }

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 18px;
    margin-top: 22px;
    margin-bottom: 8px;
    padding: 8px 0 0 0;

    .toolbar-left-group {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
      .el-button {
        border-radius: 6px;
        font-size: 15px;
        &.el-button--primary {
          background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
          color: #fff;
          border: none;
        }
        &.el-button--danger {
          background: #fff0f0;
          color: #f56c6c;
          border: 1px solid #fbc4c4;
        }
      }
      .el-input {
        border-radius: 6px;
        font-size: 15px;
      }
    }
    .toolbar-right-group {
      .el-button {
        border-radius: 6px;
        font-size: 15px;
      }
    }
  }

  .el-table {
    font-size: 15px;
    th {
      font-weight: 600;
      color: #222;
      background: #f7fafd;
      border-bottom: 1.5px solid #e6e8eb;
    }
    td {
      vertical-align: middle;
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 22px;
      }
    }
    .el-table__row {
      transition: background 0.2s;
      &:hover {
        background: #f5faff;
      }
    }
    .el-table__body tr:nth-child(2n) {
      background: #fcfdff;
    }
  }

  .project-info {
    display: flex;
    align-items: center;
  }

  .project-thumbnail {
    width: 128px;
    height: 72px;
    background-color: #f0f2f5;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
    border: 1px solid #e6e8eb;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      font-size: 30px;
    }
  }

  .project-id {
    max-width: 180px;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    color: #409eff;
  }
  .project-title {
    font-weight: 500;
    color: #333;
  }
  .project-desc {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
  }
  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;
    .el-icon {
      color: #b1b1b1;
      font-size: 16px;
    }
  }
  .action-btn {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
  }

  .el-dialog {
    border-radius: 14px !important;
    .el-dialog__header {
      font-size: 18px;
      font-weight: 600;
      color: #222;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }
    .el-form {
      margin-top: 10px;
      .el-form-item {
        margin-bottom: 18px;
        .el-input, .el-textarea {
          border-radius: 6px;
          font-size: 15px;
        }
      }
    }
    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 18px;
      .el-button {
        min-width: 90px;
        border-radius: 6px;
        font-size: 15px;
      }
    }
  }

  .el-tag {
    border-radius: 6px;
    font-size: 14px;
    padding: 0 10px;
  }
}

// 适配分页器
:deep(.el-pagination) {
  margin-top: 4px;
  background: none !important;
  border-radius: 0 0 10px 10px;
  box-shadow: none !important;
  padding-bottom: 8px;
  .el-pager li {
    border-radius: 6px;
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 15px;
  }
}
</style>