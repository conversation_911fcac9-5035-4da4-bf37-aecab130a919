/**
 * @file timelineUtils.ts
 * @description 时间轴操作相关的工具函数集合
 *              提供时间轴数据处理、片段操作、轨道管理等通用功能
 *              从 useVideoEditor.ts 中抽取，降低代码复杂度，提高可维护性
 */

import type { Timeline, TimelineClip, ProcessedClip, DraggedClipData } from '../types/videoEdit';

/**
 * @description 支持的轨道类型枚举
 */
export type TrackType = 'video' | 'audio' | 'subtitle' | 'image';

/**
 * @interface TimelineOperationResult
 * @description 时间轴操作的统一返回结果格式
 */
export interface TimelineOperationResult {
  success: boolean;
  timeline?: Timeline;
  error?: string;
}

/**
 * @interface TrackClipsResult
 * @description 获取轨道片段的结果接口
 */
export interface TrackClipsResult {
  trackClips: TimelineClip[] | undefined;
  found: boolean;
}

/**
 * @interface SnapPoint
 * @description 吸附点信息
 */
export interface SnapPoint {
  position: number; // 像素位置
  time: number;     // 对应的时间（秒）
  type: 'start' | 'end'; // 是片段开始还是结束边界
}

/**
 * @interface ClipWithPosition
 * @description 带位置信息的片段数据
 */
export interface ClipWithPosition extends ProcessedClip {
  type: 'video' | 'audio' | 'subtitle';
  trackIndex: number;
  clipIndex: number;
}

/**
 * @interface TrackBounds
 * @description 轨道边界信息
 */
export interface TrackBounds {
  startIndex: number;
  endIndex: number;
}

/**
 * @interface DragCalculationResult
 * @description 拖拽计算结果
 */
export interface DragCalculationResult {
  targetTrackIndex: number | null;
  isValidDrop: boolean;
  isNewTrack: boolean;
  insertPosition?: 'above' | 'below' | 'replace'; // 插入位置：上方、下方、替换
  snapZone?: 'top' | 'center' | 'bottom'; // 吸附区域
}

/**
 * @interface TrackCounts
 * @description 轨道数量统计
 */
export interface TrackCounts {
  video: number;
  audio: number;
  subtitle: number;
}

/**
 * @function getAllSnapPoints
 * @description 收集所有轨道所有片段的吸附点（开始和结束边界）
 * @param {Timeline} timeline - 时间轴数据
 * @param {DraggedClipData | null} draggedClip - 正在拖拽的片段信息，将被排除
 * @param {number} pixelsPerSecond - 像素每秒的换算比例
 * @returns {SnapPoint[]} 所有吸附点数组
 */
export function getAllSnapPoints(
  timeline: Timeline, 
  draggedClip: DraggedClipData | null,
  pixelsPerSecond: number
): SnapPoint[] {
  const snapPoints: SnapPoint[] = [];

  // 添加时间轴起点
  snapPoints.push({ position: 0, time: 0, type: 'start' });

  // 收集所有视频轨道的片段
  timeline.VideoTracks?.forEach((track, trackIndex) => {
    track.VideoTrackClips?.forEach((clip, clipIndex) => {
      // 排除正在拖拽的片段
      if (draggedClip && draggedClip.type === 'video' && 
          draggedClip.trackIndex === trackIndex && draggedClip.clipIndex === clipIndex) {
        return;
      }
      
      const startPos = clip.TimelineIn * pixelsPerSecond;
      const endPos = clip.TimelineOut * pixelsPerSecond;
      
      snapPoints.push({ position: startPos, time: clip.TimelineIn, type: 'start' });
      snapPoints.push({ position: endPos, time: clip.TimelineOut, type: 'end' });
    });
  });

  // 收集所有音频轨道的片段
  timeline.AudioTracks?.forEach((track, trackIndex) => {
    track.AudioTrackClips?.forEach((clip, clipIndex) => {
      // 排除正在拖拽的片段
      if (draggedClip && draggedClip.type === 'audio' && 
          draggedClip.trackIndex === trackIndex && draggedClip.clipIndex === clipIndex) {
        return;
      }
      
      const startPos = clip.TimelineIn * pixelsPerSecond;
      const endPos = clip.TimelineOut * pixelsPerSecond;
      
      snapPoints.push({ position: startPos, time: clip.TimelineIn, type: 'start' });
      snapPoints.push({ position: endPos, time: clip.TimelineOut, type: 'end' });
    });
  });

  // 收集所有字幕轨道的片段
  timeline.SubtitleTracks?.forEach((track, trackIndex) => {
    track.SubtitleTrackClips?.forEach((clip, clipIndex) => {
      // 排除正在拖拽的片段
      if (draggedClip && draggedClip.type === 'subtitle' && 
          draggedClip.trackIndex === trackIndex && draggedClip.clipIndex === clipIndex) {
        return;
      }
      
      const startPos = clip.TimelineIn * pixelsPerSecond;
      const endPos = clip.TimelineOut * pixelsPerSecond;
      
      snapPoints.push({ position: startPos, time: clip.TimelineIn, type: 'start' });
      snapPoints.push({ position: endPos, time: clip.TimelineOut, type: 'end' });
    });
  });

  return snapPoints;
}

/**
 * @function findBestSnapPosition
 * @description 根据拖拽片段的位置找到最佳吸附位置
 * @param {number} draggedLeftPx - 拖拽片段左边界位置（像素）
 * @param {number} draggedRightPx - 拖拽片段右边界位置（像素）
 * @param {SnapPoint[]} snapPoints - 所有吸附点
 * @param {number} snapThreshold - 吸附阈值（像素）
 * @returns {number | null} 最佳吸附位置（像素），如果没有找到吸附点则返回null
 */
export function findBestSnapPosition(
  draggedLeftPx: number,
  draggedRightPx: number,
  snapPoints: SnapPoint[],
  snapThreshold: number
): number | null {
  let bestSnapPos: number | null = null;
  let minDistance = snapThreshold;

  for (const snapPoint of snapPoints) {
    // 检查左边界吸附
    const leftDistance = Math.abs(draggedLeftPx - snapPoint.position);
    if (leftDistance < minDistance) {
      bestSnapPos = snapPoint.position;
      minDistance = leftDistance;
    }

    // 检查右边界吸附
    const rightDistance = Math.abs(draggedRightPx - snapPoint.position);
    if (rightDistance < minDistance) {
      // 计算左边界应该在的位置
      bestSnapPos = snapPoint.position - (draggedRightPx - draggedLeftPx);
      minDistance = rightDistance;
    }
  }

  return bestSnapPos;
}

/**
 * @function deepCopyTimeline
 * @description 深拷贝时间轴数据，确保 Vue 响应性更新时不污染原始数据
 * @param {Timeline} timeline - 原始时间轴数据
 * @returns {Timeline} 深拷贝后的时间轴数据
 * @example
 * ```typescript
 * const copiedTimeline = deepCopyTimeline(originalTimeline);
 * // 可以安全地修改 copiedTimeline 而不影响原始数据
 * ```
 */
export function deepCopyTimeline(timeline: Timeline): Timeline {
  return JSON.parse(JSON.stringify(timeline));
}

/**
 * @function getTrackClips
 * @description 根据轨道类型获取对应的片段数组
 *              支持视频、音频、字幕、图片等多种轨道类型
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引，默认为 0（通常只有一个轨道）
 * @returns {TrackClipsResult} 包含片段数组和是否找到的结果对象
 * @example
 * ```typescript
 * const { trackClips, found } = getTrackClips(timeline, 'video', 0);
 * if (found && trackClips) {
 *   console.log('视频轨道包含', trackClips.length, '个片段');
 * }
 * ```
 */
export function getTrackClips(timeline: Timeline, type: TrackType, trackIndex: number = 0): TrackClipsResult {
  let trackClips: TimelineClip[] | undefined;
  let found = false;

  switch (type) {
    case 'video':
      trackClips = timeline.VideoTracks?.[trackIndex]?.VideoTrackClips;
      found = !!(timeline.VideoTracks?.[trackIndex]?.VideoTrackClips);
      break;
    case 'audio':
      trackClips = timeline.AudioTracks?.[trackIndex]?.AudioTrackClips;
      found = !!(timeline.AudioTracks?.[trackIndex]?.AudioTrackClips);
      break;
    case 'subtitle':
      trackClips = timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips;
      found = !!(timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips);
      break;
    case 'image':
      trackClips = timeline.ImageTracks?.[trackIndex]?.ImageTrackClips;
      found = !!(timeline.ImageTracks?.[trackIndex]?.ImageTrackClips);
      break;
    default:
      console.warn(`不支持的轨道类型: ${type}`);
      return { trackClips: undefined, found: false };
  }

  return { trackClips, found };
}

/**
 * @function validateClipIndex
 * @description 验证片段索引是否在有效范围内
 * @param {TimelineClip[] | undefined} trackClips - 轨道片段数组
 * @param {number} index - 要验证的索引
 * @param {string} operation - 操作名称（用于错误提示）
 * @returns {boolean} 索引是否有效
 */
export function validateClipIndex(trackClips: TimelineClip[] | undefined, index: number, operation: string): boolean {
  if (!trackClips || trackClips.length <= index) {
    console.warn(`${operation}失败：索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`);
    return false;
  }
  return true;
}

/**
 * @function validateCutTime
 * @description 验证切割时间点是否在片段的有效范围内
 * @param {TimelineClip} clip - 要切割的片段
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {boolean} 切割时间是否有效
 */
export function validateCutTime(clip: TimelineClip, cutTime: number): boolean {
  if (cutTime <= clip.TimelineIn || cutTime >= clip.TimelineOut) {
    console.warn(
      `切割点 ${cutTime.toFixed(2)} 秒不在片段有效范围 (${clip.TimelineIn.toFixed(2)} - ${clip.TimelineOut.toFixed(2)} 秒)`
    );
    return false;
  }
  return true;
}

/**
 * @function createCutClips
 * @description 根据切割时间点创建两个新片段
 * @param {TimelineClip} originalClip - 原始片段
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {[TimelineClip, TimelineClip]} 切割后的两个片段数组
 */
export function createCutClips(originalClip: TimelineClip, cutTime: number): [TimelineClip, TimelineClip] {
  const cutPointInMedia = (cutTime - originalClip.TimelineIn) + originalClip.In;
  const newClip1: TimelineClip = {
    ...originalClip,
    TimelineOut: cutTime, // 第一个片段的结束时间为切割点
    Out: cutPointInMedia
  };

  const newClip2: TimelineClip = {
    ...originalClip,
    TimelineIn: cutTime, // 第二个片段的开始时间为切割点
    In: cutPointInMedia
  };

  return [newClip1, newClip2];
}

/**
 * @function updateClipTimeRange
 * @description 更新片段的时间范围，保持原始持续时间
 * @param {TimelineClip} clip - 要更新的片段
 * @param {number} newStartTime - 新的开始时间（秒）
 * @returns {TimelineClip} 更新后的片段（修改原对象）
 */
export function updateClipTimeRange(clip: TimelineClip, newStartTime: number): TimelineClip {
  // 计算原始片段的持续时间
  const originalDuration = clip.TimelineOut - clip.TimelineIn;

  // 更新 TimelineIn 和 TimelineOut
  clip.TimelineIn = newStartTime;
  clip.TimelineOut = newStartTime + originalDuration;

  return clip;
}

/**
 * @function cutClipAtTime
 * @description 在指定时间点切割时间轴中的片段
 *              这是一个高级函数，整合了验证、切割、更新等逻辑
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引
 * @param {number} clipIndex - 片段索引
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function cutClipAtTime(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number,
  cutTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '切割')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 获取原始片段并验证切割时间
  const originalClip = trackClips![clipIndex];
  if (!validateCutTime(originalClip, cutTime)) {
    const error = `切割点 ${cutTime.toFixed(2)} 秒不在片段有效范围 (${originalClip.TimelineIn.toFixed(2)} - ${originalClip.TimelineOut.toFixed(2)} 秒)`;
    return { success: false, error };
  }

  // 5. 创建两个新片段
  const [newClip1, newClip2] = createCutClips(originalClip, cutTime);

  // 6. 在原位置替换为两个新片段
  trackClips!.splice(clipIndex, 1, newClip1, newClip2);

  return { success: true, timeline: newTimeline };
}

/**
 * @function deleteClipAtIndex
 * @description 删除时间轴中指定索引的片段
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引
 * @param {number} clipIndex - 片段索引
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function deleteClipAtIndex(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '删除')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 删除片段
  const deletedClip = trackClips!.splice(clipIndex, 1)[0];
  console.log(`已删除 ${type} 轨道 ${trackIndex} 中的片段:`, deletedClip);

  return { success: true, timeline: newTimeline };
}

/**
 * @function updateClipTimeAtIndex
 * @description 更新时间轴中指定片段的时间范围
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引
 * @param {number} clipIndex - 片段索引
 * @param {number} newStartTime - 新的开始时间（秒）
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function updateClipTimeAtIndex(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number,
  newStartTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '时间更新')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 更新片段时间范围
  const clipToUpdate = trackClips![clipIndex];
  updateClipTimeRange(clipToUpdate, newStartTime);

  return { success: true, timeline: newTimeline };
}

/**
 * @function moveClipToTrack
 * @description 将片段移动到不同轨道，支持同类型轨道间的跨轨道移动
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} sourceTrackIndex - 源轨道索引
 * @param {number} clipIndex - 片段索引
 * @param {number} targetTrackIndex - 目标轨道索引
 * @param {number} newStartTime - 新的开始时间（秒）
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function moveClipToTrack(
  timeline: Timeline,
  type: TrackType,
  sourceTrackIndex: number,
  clipIndex: number,
  targetTrackIndex: number,
  newStartTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取源轨道的片段数组
  const { trackClips: sourceTrackClips, found: sourceFound } = getTrackClips(newTimeline, type, sourceTrackIndex);
  if (!sourceFound) {
    const error = `未找到源 ${type} 轨道 ${sourceTrackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(sourceTrackClips, clipIndex, '移动')) {
    const error = `片段索引 ${clipIndex} 超出范围（总共 ${sourceTrackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 获取要移动的片段
  const clipToMove = sourceTrackClips![clipIndex];
  
  // 5. 确保目标轨道存在
  const targetTrackExists = ensureTrackExists(newTimeline, type, targetTrackIndex);
  if (!targetTrackExists.success) {
    return { success: false, error: targetTrackExists.error };
  }

  // 6. 获取目标轨道
  const { trackClips: targetTrackClips, found: targetFound } = getTrackClips(newTimeline, type, targetTrackIndex);
  if (!targetFound) {
    const error = `未找到目标 ${type} 轨道 ${targetTrackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 7. 从源轨道移除片段
  sourceTrackClips!.splice(clipIndex, 1);

  // 8. 更新片段的时间位置
  const updatedClip = updateClipTimeRange({ ...clipToMove }, newStartTime);

  // 9. 将片段添加到目标轨道
  targetTrackClips!.push(updatedClip);

  // 10. 按时间排序目标轨道的片段
  targetTrackClips!.sort((a, b) => a.TimelineIn - b.TimelineIn);

  console.log(`✅ 片段移动成功: 从 ${type} 轨道 ${sourceTrackIndex} 移动到轨道 ${targetTrackIndex}`);
  return { success: true, timeline: newTimeline };
}

/**
 * @function ensureTrackExists
 * @description 确保指定索引的轨道存在，如果不存在则创建
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引
 * @returns {TimelineOperationResult} 操作结果
 */
export function ensureTrackExists(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number
): TimelineOperationResult {
  let tracksArray: any[] | undefined;
  let trackClipsProperty: string;
  let trackTemplate: any;

  // 根据轨道类型获取对应的数组和模板
  switch (type) {
    case 'video':
      tracksArray = timeline.VideoTracks;
      trackClipsProperty = 'VideoTrackClips';
      trackTemplate = { Id: trackIndex, VideoTrackClips: [] };
      break;
    case 'audio':
      tracksArray = timeline.AudioTracks;
      trackClipsProperty = 'AudioTrackClips';
      trackTemplate = { Id: trackIndex, AudioTrackClips: [] };
      break;
    case 'subtitle':
      tracksArray = timeline.SubtitleTracks;
      trackClipsProperty = 'SubtitleTrackClips';
      trackTemplate = { Id: trackIndex, SubtitleTrackClips: [] };
      break;
    default:
      return { success: false, error: `不支持的轨道类型: ${type}` };
  }

  // 确保轨道数组存在
  if (!tracksArray) {
    switch (type) {
      case 'video':
        timeline.VideoTracks = [];
        tracksArray = timeline.VideoTracks;
        break;
      case 'audio':
        timeline.AudioTracks = [];
        tracksArray = timeline.AudioTracks;
        break;
      case 'subtitle':
        timeline.SubtitleTracks = [];
        tracksArray = timeline.SubtitleTracks;
        break;
    }
  }

  // 确保轨道索引处有轨道对象
  while (tracksArray!.length <= trackIndex) {
    const newTrack = { ...trackTemplate, Id: tracksArray!.length };
    tracksArray!.push(newTrack);
    console.log(`📝 创建新的 ${type} 轨道 ${tracksArray!.length - 1}`);
  }

  return { success: true };
}

/**
 * @function normalizeTimeline - 归一化Timeline数据
 * @description 将API返回的Timeline数据进行标准化处理，确保字幕片段正确分类
 * - 将Type为"Text"的片段从VideoTracks中提取出来，归入SubtitleTracks
 * - 修复轨道结构，确保字幕片段本地渲染，不参与媒体资源加载
 * @param {Timeline} rawTimeline - 原始时间轴数据
 * @returns {Timeline} 处理后的标准化时间轴数据
 */
export function normalizeTimeline(rawTimeline: Timeline): Timeline {
  console.log('🔧 开始Timeline数据归一化...');
  
  // 深拷贝原始数据，避免修改源对象
  const timeline = deepCopyTimeline(rawTimeline);
  let totalExtractedSubtitleClips = 0;

  // 确保SubtitleTracks数组存在
  if (!timeline.SubtitleTracks) {
    timeline.SubtitleTracks = [];
  }

  // 遍历所有VideoTracks，提取字幕片段
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((videoTrack, trackIndex) => {
      if (!videoTrack.VideoTrackClips) return;

      // 分离视频片段和字幕片段
      const actualVideoClips: TimelineClip[] = [];
      const subtitleClips: TimelineClip[] = [];

      videoTrack.VideoTrackClips.forEach(clip => {
        if (clip.Type === 'Text') {
          // 字幕片段：移入字幕数组
          subtitleClips.push(clip);
          totalExtractedSubtitleClips++;
          console.log(`  📝 提取字幕片段: Content="${clip.Content}", TrackId=${clip.TrackId}, Duration=${clip.Duration}`);
        } else {
          // 实际视频片段：保留在视频数组中
          actualVideoClips.push(clip);
        }
      });

      // 更新VideoTrackClips，只保留真正的视频片段
      videoTrack.VideoTrackClips = actualVideoClips;

      // 如果提取到了字幕片段，需要创建对应的字幕轨道
      if (subtitleClips.length > 0) {
        // 查找是否已存在对应TrackId的字幕轨道
        let subtitleTrack = timeline.SubtitleTracks!.find(track => track.Id === videoTrack.Id);
        
        if (!subtitleTrack) {
          // 创建新的字幕轨道
          subtitleTrack = {
            Id: videoTrack.Id,
            Type: 'Subtitle',
            Visible: true,
            Disabled: false,
            Count: 0,
            SubtitleTrackClips: []
          };
          timeline.SubtitleTracks!.push(subtitleTrack);
          console.log(`  🎯 创建字幕轨道 (Id: ${subtitleTrack.Id})`);
        }

        // 将字幕片段添加到字幕轨道
        subtitleTrack.SubtitleTrackClips.push(...subtitleClips);
        subtitleTrack.Count = subtitleTrack.SubtitleTrackClips.length;
      }
    });
  }

  console.log(`✅ Timeline归一化完成，共提取${totalExtractedSubtitleClips}个字幕片段到SubtitleTracks`);
  return timeline;
}

// ======================= 跨轨道拖拽工具函数 =======================

/**
 * @function getTrackBounds
 * @description 获取指定轨道类型的边界索引
 * @param {TrackType} trackType - 轨道类型
 * @param {number} videoCount - 视频轨道数量
 * @param {number} audioCount - 音频轨道数量  
 * @param {number} subtitleCount - 字幕轨道数量
 * @returns {TrackBounds} 轨道边界信息
 */
export function getTrackBounds(
  trackType: TrackType, 
  videoCount: number, 
  audioCount: number, 
  subtitleCount: number
): TrackBounds {
  switch (trackType) {
    case 'video':
      return { startIndex: 0, endIndex: Math.max(0, videoCount - 1) };
    case 'audio':
      return { startIndex: videoCount, endIndex: videoCount + Math.max(0, audioCount - 1) };
    case 'subtitle':
      return { startIndex: videoCount + audioCount, endIndex: videoCount + audioCount + Math.max(0, subtitleCount - 1) };
    default:
      return { startIndex: 0, endIndex: 0 };
  }
}

/**
 * @function calculateTargetTrackIndex
 * @description 根据鼠标位置精确计算目标轨道索引，支持轨道间插入和吸附，参考专业剪辑软件的拖拽行为
 * @param {number} mouseY - 鼠标Y坐标
 * @param {TrackType} trackType - 轨道类型
 * @param {DOMRect} scrollAreaRect - 滚动区域矩形信息
 * @param {number} scrollTop - 垂直滚动偏移量
 * @param {number} trackHeight - 轨道高度
 * @param {any[]} displayTracks - 显示轨道数组
 * @param {any[]} allTracks - 所有轨道数组
 * @param {number} sourceTrackIndex - 拖拽源轨道索引（用于判断拖拽方向）
 * @returns {DragCalculationResult} 拖拽计算结果
 */
export function calculateTargetTrackIndex(
  mouseY: number,
  trackType: TrackType,
  scrollAreaRect: DOMRect,
  scrollTop: number,
  trackHeight: number,
  displayTracks: any[],
  allTracks: any[],
  sourceTrackIndex?: number
): DragCalculationResult {
  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 参考阿里云智能媒体生产的轨道边界计算
  const trackBounds = displayTracks.map((track, index) => ({
    index,
    top: index * trackHeight,
    center: index * trackHeight + trackHeight / 2,
    bottom: (index + 1) * trackHeight,
    track
  }));

  // 动态阈值：根据轨道高度调整吸附区域，更精准的判断
  const insertThreshold = trackHeight * 0.25;  // 上下25%区域为插入区域
  const centerZone = trackHeight * 0.5;        // 中间50%为替换区域
  
  // 查找当前鼠标所在的轨道区域
  let currentTrackIndex = Math.floor(relativeY / trackHeight);
  currentTrackIndex = Math.max(0, Math.min(currentTrackIndex, displayTracks.length - 1));
  
  // 计算在当前轨道内的相对位置（0-1）
  const trackStartY = currentTrackIndex * trackHeight;
  const relativePositionInTrack = (relativeY - trackStartY) / trackHeight;
  
  // 根据相对位置和拖拽方向智能判断意图
  let insertPosition: 'above' | 'below' | 'replace' = 'replace';
  let targetTrackIndex = currentTrackIndex;
  let isNewTrack = false;
  
  // 处理拖拽到轨道区域外的情况（更严格的边界检查）
  if (relativeY < -insertThreshold) {
    // 拖拽到最上方，插入到第一个轨道之前
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  const totalTracksHeight = displayTracks.length * trackHeight;
  if (relativeY > totalTracksHeight + insertThreshold) {
    // 拖拽到最下方，添加新轨道
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below', 
      snapZone: 'bottom'
    };
  }
  
  // 智能判断拖拽意图（参考专业剪辑软件的行为）
  if (sourceTrackIndex !== undefined) {
    const isDraggingDown = sourceTrackIndex < currentTrackIndex;
    const isDraggingUp = sourceTrackIndex > currentTrackIndex;
    
    // 上边缘区域（0-25%）
    if (relativePositionInTrack < 0.25) {
      if (isDraggingDown) {
        // 从上往下拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else if (isDraggingUp && relativePositionInTrack < 0.15) {
        // 从下往上拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 下边缘区域（75%-100%）
    else if (relativePositionInTrack > 0.75) {
      if (isDraggingUp) {
        // 从下往上拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else if (isDraggingDown && relativePositionInTrack > 0.85) {
        // 从上往下拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 中心区域（25%-75%）
    else {
      // 中心区域优先考虑替换，除非拖拽距离很远
      const trackDistance = Math.abs(sourceTrackIndex - currentTrackIndex);
      if (trackDistance > 2) {
        // 长距离拖拽时，根据拖拽方向倾向于插入
        if (isDraggingDown && relativePositionInTrack > 0.6) {
          insertPosition = 'below';
          targetTrackIndex = currentTrackIndex + 1;
          isNewTrack = true;
        } else if (isDraggingUp && relativePositionInTrack < 0.4) {
          insertPosition = 'above';
          targetTrackIndex = currentTrackIndex;
          isNewTrack = true;
        } else {
          insertPosition = 'replace';
          targetTrackIndex = currentTrackIndex;
        }
      } else {
        // 短距离拖拽优先替换
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
  } else {
    // 没有源轨道信息时的简单判断
    if (relativePositionInTrack < 0.2) {
      insertPosition = 'above';
      targetTrackIndex = currentTrackIndex;
      isNewTrack = true;
    } else if (relativePositionInTrack > 0.8) {
      insertPosition = 'below';
      targetTrackIndex = currentTrackIndex + 1;
      isNewTrack = true;
    } else {
      insertPosition = 'replace';
      targetTrackIndex = currentTrackIndex;
    }
  }
  
  // 处理目标轨道不存在的情况
  const targetTrack = displayTracks[currentTrackIndex];
  if (!targetTrack) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }
  
  // 修正目标轨道索引（处理预览轨道）
  if (insertPosition === 'replace') {
    targetTrackIndex = targetTrack.originalIndex === -1 ? 0 : targetTrack.originalIndex;
    isNewTrack = targetTrack.originalIndex === -1 || targetTrackIndex >= allTracks.length;
  }
  
  // 确定吸附区域
  let snapZone: 'top' | 'center' | 'bottom' = 'center';
  if (insertPosition === 'above') {
    snapZone = 'top';
  } else if (insertPosition === 'below') {
    snapZone = 'bottom';
  }

  return { 
    targetTrackIndex, 
    isValidDrop: true, 
    isNewTrack,
    insertPosition,
    snapZone
  };
}

/**
 * @function createPreviewTrack
 * @description 创建预览轨道对象
 * @param {TrackType} trackType - 轨道类型
 * @param {number} previewTrackIndex - 预览轨道索引
 * @returns {any} 预览轨道对象
 */
export function createPreviewTrack(trackType: TrackType, previewTrackIndex: number): any {
  return {
    trackIndex: previewTrackIndex,
    trackId: `preview_${trackType}_${previewTrackIndex}`,
    clips: [],
    uiIndex: -1,
    originalIndex: previewTrackIndex,
    isDefaultTrack: false,
    isPreviewTrack: true
  };
}

/**
 * @function calculateDropZone
 * @description 精确计算拖拽放置区域，支持专业剪辑软件的轨道吸附行为
 * @param {number} mouseY - 鼠标Y坐标
 * @param {number} trackHeight - 轨道高度  
 * @param {number} trackIndex - 当前轨道索引
 * @param {number} relativeY - 相对Y坐标
 * @returns {object} 放置区域信息
 */
export function calculateDropZone(
  mouseY: number,
  trackHeight: number,
  trackIndex: number,
  relativeY: number
): {
  zone: 'insert-above' | 'track-center' | 'insert-below';
  insertIndex?: number;
  confidence: number;
} {
  const trackTop = trackIndex * trackHeight;
  const trackCenter = trackTop + trackHeight / 2;
  const trackBottom = trackTop + trackHeight;
  
  // 计算在轨道内的相对位置（0-1）
  const positionInTrack = (relativeY - trackTop) / trackHeight;
  
  // 定义吸附区域（上方30%，中间40%，下方30%）
  const insertAboveZone = 0.3;
  const insertBelowZone = 0.7;
  
  // 计算距离各个区域的距离，用于确定置信度
  const distanceToTop = Math.abs(positionInTrack);
  const distanceToCenter = Math.abs(positionInTrack - 0.5);
  const distanceToBottom = Math.abs(positionInTrack - 1);
  
  if (positionInTrack < insertAboveZone) {
    return {
      zone: 'insert-above',
      insertIndex: trackIndex,
      confidence: 1 - distanceToTop // 越接近顶部，置信度越高
    };
  } else if (positionInTrack > insertBelowZone) {
    return {
      zone: 'insert-below', 
      insertIndex: trackIndex + 1,
      confidence: distanceToBottom // 越接近底部，置信度越高
    };
  } else {
    return {
      zone: 'track-center',
      confidence: 1 - distanceToCenter * 2 // 越接近中心，置信度越高
    };
  }
}

/**
 * @function getVisualFeedbackStyle
 * @description 根据拖拽状态生成视觉反馈样式
 * @param {DragCalculationResult} dragResult - 拖拽计算结果
 * @param {number} trackHeight - 轨道高度
 * @returns {object} 视觉反馈样式对象
 */
export function getVisualFeedbackStyle(
  dragResult: DragCalculationResult, 
  trackHeight: number
): {
  showInsertLine: boolean;
  insertLinePosition?: number;
  highlightTrack: boolean;
  insertLineStyle?: 'solid' | 'dashed';
} {
  if (!dragResult.isValidDrop) {
    return { showInsertLine: false, highlightTrack: false };
  }

  const { targetTrackIndex, insertPosition, isNewTrack } = dragResult;
  
  if (isNewTrack && insertPosition) {
    let insertLinePosition = 0;
    
    if (insertPosition === 'above') {
      insertLinePosition = targetTrackIndex! * trackHeight;
    } else if (insertPosition === 'below') {
      insertLinePosition = (targetTrackIndex! + 1) * trackHeight;
    }
    
    return {
      showInsertLine: true,
      insertLinePosition,
      highlightTrack: false,
      insertLineStyle: 'solid'
    };
  } else {
    // 替换到现有轨道
    return {
      showInsertLine: false,
      highlightTrack: true
    };
  }
}

/**
 * @function addPreviewTrackToDisplayTracks
 * @description 向显示轨道数组中添加预览轨道，支持精确的轨道插入和吸附
 * @param {any[]} tracks - 原始轨道数组
 * @param {TrackType} trackType - 轨道类型
 * @param {DragCalculationResult} dragResult - 拖拽计算结果
 * @param {boolean} isShowingPreview - 是否显示预览
 * @returns {any[]} 包含预览轨道的轨道数组
 */
export function addPreviewTrackToDisplayTracks(
  tracks: any[],
  trackType: TrackType,
  dragResult: DragCalculationResult,
  isShowingPreview: boolean
): any[] {
  if (!isShowingPreview || !dragResult.isValidDrop || !dragResult.isNewTrack) {
    return tracks.map((track, index) => ({
      ...track,
      uiIndex: index,
      isHighlighted: !dragResult.isNewTrack && dragResult.targetTrackIndex === track.originalIndex
    }));
  }

  const { targetTrackIndex, insertPosition } = dragResult;
  const result = [...tracks];
  
  // 创建预览轨道
  const previewTrack = {
    ...createPreviewTrack(trackType, targetTrackIndex!),
    insertPosition,
    isHighlighted: true
  };

  // 根据插入位置添加预览轨道
  if (insertPosition === 'above') {
    // 找到对应的UI索引位置进行插入
    const uiInsertIndex = result.findIndex(track => 
      track.originalIndex >= targetTrackIndex!
    );
    
    if (uiInsertIndex >= 0) {
      result.splice(uiInsertIndex, 0, previewTrack);
    } else {
      result.unshift(previewTrack); // 插入到最前面
    }
  } else if (insertPosition === 'below') {
    // 找到对应的UI索引位置进行插入  
    const uiInsertIndex = result.findIndex(track => 
      track.originalIndex >= targetTrackIndex!
    );
    
    if (uiInsertIndex >= 0) {
      result.splice(uiInsertIndex, 0, previewTrack);
    } else {
      result.push(previewTrack); // 插入到最后面
    }
  } else {
    // 默认插入到末尾
    result.push(previewTrack);
  }

  // 重新分配UI索引
  result.forEach((track, index) => {
    track.uiIndex = index;
  });
  
  return result;
}

/**
 * @function insertTrackAtPosition
 * @description 在指定位置插入新轨道
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} trackType - 轨道类型
 * @param {number} insertIndex - 插入位置索引
 * @param {'above' | 'below'} insertPosition - 插入位置
 * @returns {TimelineOperationResult} 操作结果
 */
export function insertTrackAtPosition(
  timeline: Timeline,
  trackType: TrackType,
  insertIndex: number,
  insertPosition: 'above' | 'below' = 'below'
): TimelineOperationResult {
  const newTimeline = deepCopyTimeline(timeline);
  
  try {
    let tracksArray: any[] | undefined;
    let newTrack: any;
    
    // 根据轨道类型获取对应的数组
    switch (trackType) {
      case 'video':
        if (!newTimeline.VideoTracks) newTimeline.VideoTracks = [];
        tracksArray = newTimeline.VideoTracks;
        newTrack = { Id: insertIndex, VideoTrackClips: [] };
        break;
      case 'audio':
        if (!newTimeline.AudioTracks) newTimeline.AudioTracks = [];
        tracksArray = newTimeline.AudioTracks;
        newTrack = { Id: insertIndex, AudioTrackClips: [] };
        break;
      case 'subtitle':
        if (!newTimeline.SubtitleTracks) newTimeline.SubtitleTracks = [];
        tracksArray = newTimeline.SubtitleTracks;
        newTrack = { Id: insertIndex, SubtitleTrackClips: [] };
        break;
      default:
        return { success: false, error: `不支持的轨道类型: ${trackType}` };
    }
    
    // 根据插入位置调整索引
    const actualInsertIndex = insertPosition === 'above' ? insertIndex : insertIndex + 1;
    
    // 插入新轨道
    tracksArray.splice(actualInsertIndex, 0, newTrack);
    
    // 重新分配轨道ID
    tracksArray.forEach((track, index) => {
      track.Id = index;
    });
    
    console.log(`✅ 成功插入${trackType}轨道到位置 ${actualInsertIndex}`);
    return { success: true, timeline: newTimeline };
  } catch (error) {
    const errorMsg = `插入轨道失败: ${error}`;
    console.error(errorMsg);
    return { success: false, error: errorMsg };
  }
}

/**
 * @function processTrackData
 * @description 处理轨道数据，转换为显示格式
 * @param {any[]} rawTracks - 原始轨道数据
 * @param {(clip: any) => any} clipMapper - 片段映射函数
 * @returns {any[]} 处理后的轨道数据
 */
export function processTrackData(rawTracks: any[], clipMapper: (clip: any) => any): any[] {
  if (!rawTracks) return [];
  
  const tracks = rawTracks.map((track, trackIndex) => {
    // 根据轨道类型获取对应的片段数组
    let clips: any[] = [];
    if (track.VideoTrackClips) {
      clips = track.VideoTrackClips.map(clipMapper);
    } else if (track.AudioTrackClips) {
      clips = track.AudioTrackClips.map(clipMapper);  
    } else if (track.SubtitleTrackClips) {
      clips = track.SubtitleTrackClips.map(clipMapper);
    }
    
    return { trackIndex, trackId: track.Id, clips };
  }).filter(track => track.clips.length > 0);
  
  // 逆序返回：让JSON数组中靠后的轨道显示在UI的上面
  return tracks.reverse().map((track, uiIndex) => ({
    ...track,
    uiIndex, // UI中的显示索引
    originalIndex: tracks.length - 1 - uiIndex // 原始数组中的索引
  }));
}

/**
 * @function createDefaultDisplayTrack
 * @description 创建默认显示轨道
 * @returns {any} 默认轨道对象
 */
export function createDefaultDisplayTrack(): any {
  return {
    trackIndex: -1,
    trackId: undefined,
    clips: [],
    uiIndex: 0,
    originalIndex: -1,
    isDefaultTrack: true
  };
}

/**
 * @function smartTrackDragCalculation
 * @description 智能轨道拖拽计算，参考专业剪辑软件的交互逻辑
 * @param {object} params - 拖拽参数
 * @returns {DragCalculationResult} 拖拽计算结果
 */
export function smartTrackDragCalculation(params: {
  mouseY: number;
  trackType: TrackType;
  scrollAreaRect: DOMRect;
  scrollTop: number;
  trackHeight: number;
  displayTracks: any[];
  allTracks: any[];
  sourceTrackIndex?: number;
  dragStartY?: number;
  isDraggingFromLibrary?: boolean; // 是否从素材库拖拽
}): DragCalculationResult {
  const {
    mouseY,
    trackType,
    scrollAreaRect,
    scrollTop,
    trackHeight,
    displayTracks,
    allTracks,
    sourceTrackIndex,
    dragStartY,
    isDraggingFromLibrary = false
  } = params;

  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 如果没有任何轨道，创建第一个轨道
  if (displayTracks.length === 0) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'center'
    };
  }

  // 计算所有轨道的边界区域
  const trackRegions = displayTracks.map((track, uiIndex) => {
    const top = uiIndex * trackHeight;
    const bottom = top + trackHeight;
    const center = top + trackHeight / 2;
    
    return {
      uiIndex,
      track,
      top,
      center, 
      bottom,
      originalIndex: track.originalIndex
    };
  });

  // 添加拖拽区域边界（用于创建新轨道）
  const topBoundary = trackRegions[0]?.top || 0;
  const bottomBoundary = trackRegions[trackRegions.length - 1]?.bottom || trackHeight;
  
  // 定义吸附阈值
  const snapThreshold = trackHeight * 0.25;
  const insertThreshold = trackHeight * 0.3;
  
  // 拖拽到轨道区域上方 - 创建新轨道到顶部
  if (relativeY < topBoundary - snapThreshold) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  // 拖拽到轨道区域下方 - 创建新轨道到底部
  if (relativeY > bottomBoundary + snapThreshold) {
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'bottom'
    };
  }

  // 在轨道区域内 - 寻找最佳匹配
  let bestMatch: any = null;
  let minDistance = Infinity;
  let insertMode = false;

  for (const region of trackRegions) {
    // 计算到各个关键点的距离
    const distanceToTop = Math.abs(relativeY - region.top);
    const distanceToCenter = Math.abs(relativeY - region.center);
    const distanceToBottom = Math.abs(relativeY - region.bottom);
    
    const minRegionDistance = Math.min(distanceToTop, distanceToCenter, distanceToBottom);
    
    if (minRegionDistance < minDistance) {
      minDistance = minRegionDistance;
      bestMatch = region;
      
      // 判断是否进入插入模式
      insertMode = (distanceToTop < insertThreshold) || (distanceToBottom < insertThreshold);
    }
  }

  if (!bestMatch) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }

  // 计算拖拽意图
  const dragDirection = sourceTrackIndex !== undefined && dragStartY !== undefined ?
    (mouseY > dragStartY ? 'down' : 'up') : null;
  
  const positionInTrack = (relativeY - bestMatch.top) / trackHeight;
  
  // 智能判断最终操作
  let finalResult: DragCalculationResult;
  
  if (insertMode) {
    // 插入模式：创建新轨道
    const distanceToTop = Math.abs(relativeY - bestMatch.top);
    const distanceToBottom = Math.abs(relativeY - bestMatch.bottom);
    
    if (distanceToTop < distanceToBottom) {
      // 插入到上方
      finalResult = {
        targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex : 0,
        isValidDrop: true,
        isNewTrack: true,
        insertPosition: 'above',
        snapZone: 'top'
      };
    } else {
      // 插入到下方
      finalResult = {
        targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex + 1 : 1,
        isValidDrop: true,
        isNewTrack: true,
        insertPosition: 'below',
        snapZone: 'bottom'
      };
    }
  } else {
    // 替换模式：放到现有轨道
    finalResult = {
      targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex : 0,
      isValidDrop: true,
      isNewTrack: bestMatch.originalIndex === -1,
      insertPosition: 'replace',
      snapZone: 'center'
    };
  }

  // 从素材库拖拽时，优先创建新轨道
  if (isDraggingFromLibrary && finalResult.insertPosition === 'replace') {
    // 根据拖拽位置决定插入方向
    if (positionInTrack < 0.5) {
      finalResult.insertPosition = 'above';
      finalResult.isNewTrack = true;
    } else {
      finalResult.insertPosition = 'below'; 
      finalResult.isNewTrack = true;
      finalResult.targetTrackIndex = (finalResult.targetTrackIndex || 0) + 1;
    }
  }

  return finalResult;
}
