import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Timeline, SelectedClip, TimelineClip } from '../types/videoEdit';
import type { Material, TemplateInfo } from '../types/template';
import { ElMessage } from 'element-plus';
import { updateTemplate } from '../api/template';
import type { UpdateTemplateRequest } from '../types/template';

import {
  cutClipAtTime,
  deleteClipAtIndex,
  updateClipTimeAtIndex,
  moveClipToTrack,
  deepCopyTimeline,
  normalizeTimeline,
  type TrackType,
  type TimelineOperationResult
} from '../utils/timelineUtils';

import {
  parseTemplateConfigForMaterials,
  convertTimelineToTemplateConfig,
  buildClipsParamFromMaterials,
  syncMaterialsWithTimeline,
  buildUpdateTemplateRequest,
  validateDataIntegrity,
  syncSingleMaterialToTimeline,
  refreshMaterialsFromTimeline
} from '../utils/templateUtils';

import {
  createPlayerController,
  createPlayerInteractionController,
  PlayerStateManager,
  type TimelinePlayerInstance
} from '../utils/playerControlUtils';

import { PlaybackValidationManager } from '../utils/timelinePlaybackUtils';

import { selectBySource, selectId, createSelector, type EditSource } from '../utils/editSourceSelector';
import { loadProjectMaterials as loadProjectMaterialsUtil, handleMaterialsLoadResultSafe } from '../utils/projectMaterialsLoader';
import { filterMaterialsByType, handleMaterialLoadError } from '../utils/materialUtils';
import { deepClone } from '@/utils/index';
import { timelineDebugger } from './debug/templateDataDebug';

/**
 * @name useVideoEditorStore
 * @description 视频编辑器的 Pinia Store，管理播放器状态、时间轴数据和模板素材。
 *              已重构为使用 Canvas 渲染引擎架构
 */
export const useVideoEditorStore = defineStore('videoEditor', () => {
  // =======================================================================
  // State - 核心响应式状态
  // =======================================================================

  const timelinePlayerRef = ref<TimelinePlayerInstance | null>(null);
  const isVideoPlaying = ref(false);
  const currentTime = ref(0);
  const lastCurrentTime = ref(0);
  const videoDurationSeconds = ref(0);
  const timeline = ref<Timeline | null>(null);
  const selectedClip = ref<SelectedClip>({ type: null, trackIndex: null, clipIndex: null });
  const templateMaterials = ref<Material[]>([]);
  const editingTemplateId = ref<string>('');
  const editingTemplateName = ref<string>('');
  const templateDetailData = ref<any>(null); // 存储完整的模板详情数据

  // **新增**: 音频电平监控状态
  const audioLevels = ref<{ left: number; right: number; peak: number }>({ left: 0, right: 0, peak: 0 });
  const audioLevelUpdateEnabled = ref<boolean>(false);

  // **新增**: 编辑来源状态
  const editSource = ref<EditSource>('cloud-editing'); // 默认为云剪辑模式

  // **新增**: 工程管理相关状态
  const editingProjectId = ref<string>(''); // 当前编辑的工程ID（云剪辑或模板工厂）
  const projectMaterials = ref<any[]>([]); // 工程关联的媒资素材
  const projectMaterialsLoading = ref<boolean>(false); // 工程素材加载状态

  // 播放器控制器实例
  const playerController = createPlayerController();
  const playerInteractionController = createPlayerInteractionController(playerController);

  // 播放校验管理器
  const playbackValidator = new PlaybackValidationManager();

  // =======================================================================
  // Getters - 计算属性
  // =======================================================================

  const videoDurationFormatted = computed(() =>
    PlayerStateManager.formatVideoDuration(videoDurationSeconds.value)
  );

  const currentTimeFormatted = computed(() =>
    PlayerStateManager.formatCurrentTime(
      currentTime.value,
      lastCurrentTime.value,
      isVideoPlaying.value
    )
  );

  // =======================================================================
  // Actions - 方法定义
  // =======================================================================

  /**
   * @action handlePause
   * @description 暴露给外部的暂停方法。用于在时间轴交互时暂停视频。
   */
  function handlePause() {
    playerController.pause();
  }

  /**
   * @action setPlayerRef
   * @description 设置播放器组件实例的引用。
   * @param {any} player - 播放器组件实例（支持 Canvas 渲染引擎）。
   */
  function setPlayerRef(player: any) {
    timelinePlayerRef.value = player;
    playerController.setPlayerRef(player);
    
    // **新增**: 设置音频电平监控
    const setupAudioLevelMonitoring = () => {
      if (player?.getRenderEngine) {
        const renderEngine = player.getRenderEngine();
        console.log('🔍 检查渲染引擎:', {
          hasRenderEngine: !!renderEngine,
          hasSetOnAudioLevelUpdate: !!(renderEngine?.setOnAudioLevelUpdate),
          playerType: typeof player,
          renderEngineType: typeof renderEngine
        });
        
        if (renderEngine?.setOnAudioLevelUpdate) {
          renderEngine.setOnAudioLevelUpdate((levels: { left: number; right: number; peak: number }) => {
            // 使用调试器记录Store接收到的音频电平数据
            timelineDebugger.logStoreAudioLevels(levels, audioLevelUpdateEnabled.value, Date.now());
            
            if (audioLevelUpdateEnabled.value) {
              audioLevels.value = levels;
            }
          });
          console.log('🎛️ 音频电平监控已连接到Canvas渲染引擎');
          return true;
        } else {
          console.warn('⚠️ 渲染引擎不支持音频电平监控或方法不存在');
          return false;
        }
      } else {
        console.warn('⚠️ 播放器不支持getRenderEngine方法');
        return false;
      }
    };
    
    // 立即尝试设置
    if (!setupAudioLevelMonitoring()) {
      // 如果立即设置失败，延迟重试
      console.log('🔄 音频监控设置失败，将延迟重试...');
      setTimeout(() => {
        console.log('🔄 延迟重试设置音频监控...');
        if (!setupAudioLevelMonitoring()) {
          console.error('❌ 音频监控设置最终失败');
        }
      }, 1000);
    }
  }

  // **新增**: 音频电平相关方法
  
  /**
   * @action enableAudioLevelMonitoring - 启用音频电平监控
   */
  function enableAudioLevelMonitoring() {
    audioLevelUpdateEnabled.value = true;
    console.log('🎛️ 音频电平监控已启用');
  }

  /**
   * @action disableAudioLevelMonitoring - 禁用音频电平监控
   */
  function disableAudioLevelMonitoring() {
    audioLevelUpdateEnabled.value = false;
    audioLevels.value = { left: 0, right: 0, peak: 0 };
    console.log('🎛️ 音频电平监控已禁用');
  }

  /**
   * @action getAudioLevels - 获取当前音频电平
   */
  function getAudioLevels() {
    return audioLevels.value;
  }

  // --- 公开的控制方法 ---
  /**
   * @action togglePlayPause - 切换播放/暂停状态
   */
  function togglePlayPause() {
    const lastTime = playerInteractionController.togglePlayPause(
      isVideoPlaying.value, 
      currentTime.value
    );
    if (lastTime !== null) {
      lastCurrentTime.value = lastTime;
    }
  }

  /**
   * @action handleReset - 重置到视频开头
   */
  function handleReset() {
    playerController.reset();
    currentTime.value = 0;
    lastCurrentTime.value = 0;
  }

  // --- 状态更新器 ---

  /**
   * @action updateIsPlaying - 更新播放状态
   * @description Canvas 渲染引擎会通过事件回调触发此方法
   */
  function updateIsPlaying(value: boolean) {
    isVideoPlaying.value = value;
  }

  /**
   * @action updateCurrentTime - 更新当前播放时间
   * @description Canvas 渲染引擎在每帧渲染时会更新此值
   */
  function updateCurrentTime(value: number) {
    currentTime.value = value;
    // 同步更新播放校验管理器的当前时间
    playbackValidator.updateCurrentTime(value);
  }

  /**
   * @action updateDuration - 更新视频总时长
   * @description 当 Canvas 渲染引擎计算出总时长后调用
   */
  function updateDuration(data: { seconds: number; formatted?: string }) {
    videoDurationSeconds.value = data.seconds;
  }
  /**
  * @action setTimeline - 设置时间轴数据
  */
  function setTimeline(data: Timeline | null) {
    if (data) {
      // 先进行数据归一化，确保字幕片段正确分类
      data = normalizeTimeline(data);
    }
    
    timeline.value = data;
    
    // 更新播放校验管理器的时间轴数据
    playbackValidator.updateTimeline(data);
  }

  /**
  * @action setSelectedClip - 设置选中的片段
  */
  function setSelectedClip(payload: SelectedClip) {
    if (selectedClip.value.type === payload.type && 
        selectedClip.value.trackIndex === payload.trackIndex && 
        selectedClip.value.clipIndex === payload.clipIndex) {
      selectedClip.value = { type: null, trackIndex: null, clipIndex: null };
    } else {
      selectedClip.value = payload;
    }
  }

  /**
   * @action handleSeek - 跳转到指定时间
   * @param {number} time - 跳转时间（秒）
   */
  function handleSeek(time: number) {
    
    // **关键修复**: 跳转时强制暂停
    if (isVideoPlaying.value) {
      playerController.pause();
      // 立即更新状态，防止音频继续播放
      isVideoPlaying.value = false;
    }
    
    // 使用播放校验管理器验证跳转位置
    const validation = playbackValidator.validateSeekPosition(time);
    const actualSeekTime = validation.actualSeekTime || time;
    
    if (!validation.isValid && validation.message) {
      console.log('⚠️ 跳转校验:', validation.message);
    }
    
    // 执行跳转
    playerController.seek(actualSeekTime);
    currentTime.value = actualSeekTime;
    playbackValidator.updateCurrentTime(actualSeekTime);
  }

  /**
   * @action handleCut - 处理视频片段切割
   * @description 已适配 Canvas 渲染引擎，切割后会自动重新渲染
   */
  function handleCut() {
    if (!timeline.value || selectedClip.value.type === null || 
        selectedClip.value.trackIndex === null || selectedClip.value.clipIndex === null) {
      return;
    }

    if (isVideoPlaying.value) {
      playerController.pause();
      isVideoPlaying.value = false;
    }

    const { type, trackIndex, clipIndex } = selectedClip.value;
    const cutTime = currentTime.value;

    const result = cutClipAtTime(timeline.value, type as TrackType, trackIndex!, clipIndex!, cutTime);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, trackIndex: null, clipIndex: null });

      // 同步模板素材数据
      try {
        const refreshedMaterials = refreshMaterialsFromTimeline(timeline.value);
        templateMaterials.value = refreshedMaterials;
      } catch (error) {
        // 静默处理错误
      }

      // **新增**: 切割后强制刷新渲染引擎
      if (timelinePlayerRef.value) {
        setTimeout(() => {
          if (timelinePlayerRef.value) {
            timelinePlayerRef.value.seek(currentTime.value);
          }
        }, 100);
      }
    }
  }

  /**
   * @action handleDeleteClip - 处理视频片段删除
   * @description 已适配 Canvas 渲染引擎，删除后会自动重新渲染
   */
  function handleDeleteClip() {
    if (!timeline.value || selectedClip.value.type === null || 
        selectedClip.value.trackIndex === null || selectedClip.value.clipIndex === null) {
      return;
    }

    if (isVideoPlaying.value) {
      playerController.pause();
      isVideoPlaying.value = false;
    }

    const { type, trackIndex, clipIndex } = selectedClip.value;
    const result = deleteClipAtIndex(timeline.value, type as TrackType, trackIndex!, clipIndex!);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, trackIndex: null, clipIndex: null });

      // 同步模板素材数据
      try {
        const refreshedMaterials = refreshMaterialsFromTimeline(timeline.value);
        templateMaterials.value = refreshedMaterials;
      } catch (error) {
        // 静默处理错误
      }

      // **新增**: 删除后强制刷新渲染引擎
      if (timelinePlayerRef.value) {
        setTimeout(() => {
          if (timelinePlayerRef.value) {
            timelinePlayerRef.value.seek(currentTime.value);
          }
        }, 100);
      }
    }
  }

  /**
   * @action updateClipTime - 更新片段时间
   */
  function updateClipTime(type: 'video' | 'audio' | 'subtitle', trackIndex: number, clipIndex: number, newStartTime: number) {
    if (!timeline.value) {
      return;
    }

    if (isVideoPlaying.value) {
      playerController.pause();
      isVideoPlaying.value = false;
    }

    const result = updateClipTimeAtIndex(timeline.value, type as TrackType, trackIndex, clipIndex, newStartTime);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, trackIndex: null, clipIndex: null });

      // **新增**: 更新后刷新渲染引擎
      if (timelinePlayerRef.value) {
        setTimeout(() => {
          if (timelinePlayerRef.value) {
            timelinePlayerRef.value.seek(currentTime.value);
          }
        }, 50);
      }
    }
  }

  /**
   * @action moveClipToTrackAction - 跨轨道移动片段
   */
  function moveClipToTrackAction(
    type: 'video' | 'audio' | 'subtitle', 
    sourceTrackIndex: number, 
    clipIndex: number, 
    targetTrackIndex: number, 
    newStartTime: number
  ) {
    if (!timeline.value) {
      return;
    }

    if (isVideoPlaying.value) {
      playerController.pause();
      isVideoPlaying.value = false;
    }

    const result = moveClipToTrack(
      timeline.value, 
      type as TrackType, 
      sourceTrackIndex, 
      clipIndex, 
      targetTrackIndex, 
      newStartTime
    );

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, trackIndex: null, clipIndex: null });

      // 刷新渲染引擎
      if (timelinePlayerRef.value) {
        setTimeout(() => {
          if (timelinePlayerRef.value) {
            timelinePlayerRef.value.seek(currentTime.value);
          }
        }, 50);
      }

      console.log('✅ 跨轨道移动完成');
    } else {
      console.error('❌ 跨轨道移动失败:', result.error);
    }
  }

  // --- 模板相关方法 ---

  /**
   * @action convertTimelineToTemplateConfig - 转换时间轴为模板配置
   */
  function convertTimelineToTemplateConfigAction(): string {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法转换为模板配置');
    }
    return convertTimelineToTemplateConfig(timeline.value, templateMaterials.value);
  }

  /**
   * @action buildClipsParamFromMaterials - 构建ClipsParam对象
   */
  function buildClipsParamFromMaterialsAction(): string {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法构建ClipsParam');
    }
    return buildClipsParamFromMaterials(templateMaterials.value, timeline.value);
  }

  /**
   * @action buildUpdateTemplateRequest - 构建更新模板请求
   */
  function buildUpdateTemplateRequestAction(): UpdateTemplateRequest {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法构建更新请求');
    }
    return buildUpdateTemplateRequest(
      editingTemplateId.value,
      editingTemplateName.value,
      timeline.value,
      templateMaterials.value
    );
  }

  /**
   * @action handleSaveTemplate - 保存模板
   */
  async function handleSaveTemplate() {
    try {
      // 数据完整性验证
      const validationResult = validateDataIntegrityAction();
      if (validationResult.issues.length > 0) {
        if (validationResult.issues.includes('时间线数据不存在') ||
          validationResult.issues.includes('编辑模板ID不存在')) {
          ElMessage.error('关键数据缺失，无法保存模板');
          return;
        }
      }

      if (!editingTemplateId.value) {
        ElMessage.error('模板ID不存在，无法保存模板');
        return;
      }

      if (!timeline.value) {
        ElMessage.error('时间线数据不存在，无法保存模板');
        return;
      }

      if (templateMaterials.value.length === 0) {
        ElMessage.warning('没有可变素材数据，将保存当前时间线为固定模板');
      }

      // 数据同步检查
      if (syncMaterialsWithTimelineAction()) {
        ElMessage.warning('检测到数据不同步，已自动调整');
      }

      const updateRequest = buildUpdateTemplateRequestAction();

      const response = await updateTemplate(updateRequest);
      ElMessage.success('模板保存成功！');

    } catch (error) {
      const errorMessage = error instanceof Error
        ? `保存模板失败: ${error.message}`
        : '保存模板失败，请稍后重试';
      ElMessage.error(errorMessage);
    }
  }

  /**
   * @action updateTemplateMaterial - 更新模板素材属性
   */
  function updateTemplateMaterial(index: number, key: keyof Material, value: any) {
    if (!templateMaterials.value || templateMaterials.value.length <= index) {
      return;
    }

    // 使用深拷贝更新 templateMaterials，避免直接修改原始数据
    const newMaterials = deepClone(templateMaterials.value) as Material[];
    const oldMaterial = newMaterials[index];
    newMaterials[index] = { ...oldMaterial, [key]: value };

    // 特殊处理：当更新 replace 状态时，同时更新 action 字段
    if (key === 'replace') {
      newMaterials[index].action = value ? '高级设置' : '';
    }

    templateMaterials.value = newMaterials;

    // **新增**：同步到 timeline 数据
    if (timeline.value) {
      const updatedMaterial = newMaterials[index];
      const success = syncSingleMaterialToTimeline(timeline.value, updatedMaterial);

      if (success) {
        // 根据不同的更新类型给出用户提示
        if (key === 'replace') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的可替换状态已更新为: ${value ? '可替换' : '不可替换'}`);
        } else if (key === 'remark') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的备注已更新`);
        } else if (key === 'advancedSettings') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的高级设置已更新`);
        }
      } else {
        // 给用户友好的提示
        if (key === 'replace' || key === 'remark' || key === 'advancedSettings') {
          ElMessage.warning(`素材 "${updatedMaterial.name}" 的设置已保存到前端，保存模板时将生效`);
        }
      }
    } else {
      // 即使没有时间轴数据，也要给用户反馈
      if (key === 'replace') {
        ElMessage.info(`素材 "${oldMaterial.name}" 的可替换状态已更新，保存模板时将生效`);
      }
    }
  }

  /**
   * @action setTemplateMaterials - 设置模板素材数据
   */
  function setTemplateMaterials(materials: Material[]) {
    templateMaterials.value = materials;
  }

  /**
   * @action setEditingTemplateInfo - 设置编辑模板信息
   */
  function setEditingTemplateInfo(templateId: string, templateName: string) {
    editingTemplateId.value = templateId;
    editingTemplateName.value = templateName;
  }

  /**
   * @action setTemplateDetailData - 设置完整的模板详情数据
   */
  function setTemplateDetailData(templateData: any) {
    templateDetailData.value = templateData;
  }

  /**
   * @action getTemplateDetailData - 获取缓存的模板详情数据
   */
  function getTemplateDetailData() {
    return templateDetailData.value;
  }

  /**
   * @action parseTemplateConfigForMaterials - 解析模板配置中的可变素材
   */
  function parseTemplateConfigForMaterialsAction(template: TemplateInfo): Material[] {
    try {
      return parseTemplateConfigForMaterials(template);
    } catch (error) {
      ElMessage.error('解析模板配置失败');
      return [];
    }
  }

  /**
   * @action syncMaterialsWithTimeline - 同步模板素材与时间线数据
   */
  function syncMaterialsWithTimelineAction(): boolean {
    if (!timeline.value) {
      return false;
    }
    return syncMaterialsWithTimeline(templateMaterials.value, timeline.value);
  }

  /**
   * @action validateDataIntegrity - 验证数据完整性
   */
  function validateDataIntegrityAction() {
    return validateDataIntegrity(
      timeline.value,
      templateMaterials.value,
      editingTemplateId.value,
      editingTemplateName.value
    );
  }

  // --- 工程相关方法 ---
  /**
   * @action setEditSource - 设置编辑来源
   */
  function setEditSource(source: EditSource) {
    editSource.value = source;
  }

  /**
   * @action setEditingProjectId - 设置当前编辑的工程ID
   * @param projectId - 从详情接口返回的实际工程ID (Project.ProjectId)
   * @param templateId - 从详情接口返回的实际模板ID (Project.TemplateId)
   */
  function setEditingProjectId(projectId: string, templateId: string = '') {
    const selector = createSelector(editSource.value);
    const currentId = selector.selectId(projectId, templateId);
    editingProjectId.value = currentId;
  }

  /**
   * @action getCurrentProjectInfo - 获取当前工程信息
   */
  function getCurrentProjectInfo() {
    const selector = createSelector(editSource.value);
    return selector.select(
      {
        type: '云剪辑工程',
        id: editingProjectId.value,
        idField: 'ProjectId'
      },
      {
        type: '模板工厂模板',
        id: editingTemplateId.value,
        idField: 'TemplateId'
      }
    );
  }

  /**
   * @action loadProjectMaterials - 加载工程关联素材
   * @param templateData - 可选的模板数据，避免重复API调用
   */
  async function loadProjectMaterials(templateData?: any) {
    projectMaterialsLoading.value = true;

    try {
      let effectiveTemplateData = templateData;
      if (!effectiveTemplateData && editSource.value === 'template-factory') {
        effectiveTemplateData = getTemplateDetailData();
      }

      const result = await loadProjectMaterialsUtil(
        editSource.value,
        editingProjectId.value,
        editingTemplateId.value,
        effectiveTemplateData
      );

      const processedResult = handleMaterialsLoadResultSafe(result, false);
      projectMaterials.value = processedResult.materials;

      if (processedResult.hasError) {
        ElMessage.error(`素材加载失败: ${result.message}`);
      }
    } catch (error) {
      projectMaterials.value = [];
      const errorMessage = handleMaterialLoadError(error);
      ElMessage.error(errorMessage);
    } finally {
      projectMaterialsLoading.value = false;
    }
  }

  /**
   * @action filterProjectMaterialsByType - 根据类型过滤工程素材
   */
  function filterProjectMaterialsByType(mediaType: 'video' | 'audio' | 'image') {
    return filterMaterialsByType(projectMaterials.value, mediaType);
  }

  // --- 播放校验相关方法 ---

  /**
   * @action getMaxTrackDuration - 获取最长轨道时长
   */
  function getMaxTrackDuration(): number {
    return playbackValidator.getMaxDuration();
  }

  /**
   * @action getPlaybackValidationInfo - 获取播放校验信息
   */
  function getPlaybackValidationInfo() {
    return {
      maxDuration: playbackValidator.getMaxDuration(),
      currentTime: playbackValidator.getCurrentTime(),
      trackDetails: playbackValidator.getTrackDetails()
    };
  }

  /**
   * @action validateCurrentPlaybackTime - 校验当前播放时间
   */
  function validateCurrentPlaybackTime() {
    return playbackValidator.validateCurrentTime();
  }

  // =======================================================================
  // 返回对象 - 暴露给组件的状态和方法
  // =======================================================================

  return {
    // State
    timelinePlayerRef,
    isVideoPlaying,
    currentTime,
    videoDurationSeconds,
    timeline,
    selectedClip,
    templateMaterials,
    editingTemplateId,
    editingTemplateName,
    editingProjectId,
    projectMaterials,
    projectMaterialsLoading,
    editSource,

    // **新增**: 音频电平状态
    audioLevels,
    audioLevelUpdateEnabled,

    // Getters
    videoDurationFormatted,
    currentTimeFormatted,

    // Actions - 播放控制
    setPlayerRef,
    togglePlayPause,
    handleReset,
    handleSeek,
    updateIsPlaying,
    updateCurrentTime,
    updateDuration,
    handlePause,
    
    // Actions - 时间轴管理
    setTimeline,
    setSelectedClip,
    handleCut,
    handleDeleteClip,
    updateClipTime,
    moveClipToTrackAction,
    
    // Actions - 模板管理
    handleSaveTemplate,
    updateTemplateMaterial,
    setTemplateMaterials,
    setEditingTemplateInfo,
    parseTemplateConfigForMaterials: parseTemplateConfigForMaterialsAction,
    convertTimelineToTemplateConfig: convertTimelineToTemplateConfigAction,
    buildClipsParamFromMaterials: buildClipsParamFromMaterialsAction,
    buildUpdateTemplateRequest: buildUpdateTemplateRequestAction,
    validateDataIntegrity: validateDataIntegrityAction,
    syncMaterialsWithTimeline: syncMaterialsWithTimelineAction,

    // Actions - 工程相关
    setEditSource,
    setEditingProjectId,
    getCurrentProjectInfo,
    loadProjectMaterials,
    filterProjectMaterialsByType,
    setTemplateDetailData,
    getTemplateDetailData,

    // Actions - 播放校验相关
    getMaxTrackDuration,
    getPlaybackValidationInfo,
    validateCurrentPlaybackTime,

    // **新增**: Actions - 音频电平监控
    enableAudioLevelMonitoring,
    disableAudioLevelMonitoring,
    getAudioLevels,
  };
});

