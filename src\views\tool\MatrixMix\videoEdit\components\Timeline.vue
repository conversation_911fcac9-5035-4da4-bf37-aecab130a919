<template>
    <div class="timeline-component">
        <!-- 1. 顶部工具栏 (Header) -->
        <div class="timeline-header">
            <!-- 播放/暂停/重置 控制按钮 -->
            <div class="controls">
                <button @click="videoEditorStore.togglePlayPause" :title="videoEditorStore.isVideoPlaying ? '暂停' : '播放'">
                    <el-icon>
                        <VideoPause v-if="videoEditorStore.isVideoPlaying" />
                        <VideoPlay v-else />
                    </el-icon>
                </button>
                <button @click="videoEditorStore.handleReset" title="回到开头">
                    <el-icon>
                        <RefreshLeft />
                    </el-icon>
                </button>
                <button @click="videoEditorStore.handleCut" title="切割" :disabled="!canCut">
                    <el-icon>
                        <Scissor />
                    </el-icon>
                </button>
                <button @click="videoEditorStore.handleDeleteClip" title="删除片段" :disabled="!canDelete">
                    <el-icon>
                        <Delete />
                    </el-icon>
                </button>
                <!-- 时间显示，直接读取 store 的 getter -->
                <span class="time-display">
                    {{ videoEditorStore.currentTimeFormatted }} / {{ videoEditorStore.videoDurationFormatted }}
                </span>
            </div>
        </div>

        <!-- 2. 时间轴主体 - 重新设计的统一滚动容器 -->
        <div class="timeline-body">
            <!-- 2.1 固定的左上角区域 -->
            <div class="timeline-corner">
                <div class="corner-placeholder"></div>
            </div>

            <!-- 2.2 固定的时间标尺区域 - 只水平滚动 -->
            <div class="timeline-ruler-section">
                <div class="ruler-scroll-container" ref="rulerScrollContainerRef" @scroll="handleRulerScroll">
                    <div class="ruler" ref="rulerRef" @mousedown="handleTimelineInteraction"
                        :style="{ width: `${rulerMarkers.length * PIXELS_PER_SECOND}px` }">
                        <div class="ruler-mark" v-for="marker in rulerMarkers" :key="`ruler-${marker.second}`"
                            :style="{ width: `${PIXELS_PER_SECOND}px` }">
                            <span class="time" v-if="marker.showLabel">{{ formatRulerTime(marker.second) }}</span>
                        </div>
                        
                        <!-- 播放头游标三角 - 跟随时间位置 -->
                        <div class="playhead-cursor" :style="{ left: `${playheadLeft}px` }">
                            <div class="playhead-top"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2.3 轨道区域 - 主滚动容器 -->
            <div class="timeline-main-scroll-area" ref="mainScrollAreaRef" @scroll="handleScroll">
                <!-- 轨道标签列 - 固定不水平滚动，只垂直滚动 -->
                <div class="track-labels-column">
                    <!-- 动态渲染视频轨道头，逆序显示 -->
                    <div class="track-header" v-for="videoTrack in displayVideoTracks" :key="`video-header-${videoTrack.originalIndex}`">
                        <i class="el-icon-video-camera track-icon"></i>
                        <span>视频轨道 {{ videoTrack.originalIndex === -1 ? 1 : videoTrack.originalIndex + 1 }}</span>
                    </div>
                    <!-- 动态渲染音频轨道头，逆序显示 -->
                    <div class="track-header" v-for="audioTrack in displayAudioTracks" :key="`audio-header-${audioTrack.originalIndex}`">
                        <i class="el-icon-headset track-icon"></i>
                        <span>音频轨道 {{ audioTrack.originalIndex === -1 ? 1 : audioTrack.originalIndex + 1 }}</span>
                    </div>
                    <!-- 动态渲染字幕轨道头，逆序显示 -->
                    <div class="track-header" v-for="subtitleTrack in displaySubtitleTracks" :key="`subtitle-header-${subtitleTrack.originalIndex}`">
                        <i class="el-icon-chat-line-square track-icon"></i>
                        <span>字幕轨道 {{ subtitleTrack.originalIndex === -1 ? 1 : subtitleTrack.originalIndex + 1 }}</span>
                    </div>
                </div>

                <!-- 轨道内容区 - 水平和垂直都可滚动 -->
                <div class="tracks-content-area" ref="tracksContentAreaRef" :style="{ width: `${rulerMarkers.length * PIXELS_PER_SECOND}px` }">
                    <!-- 视频轨道 -->
                    <div class="track" v-for="videoTrack in displayVideoTracks" :key="`video-track-${videoTrack.originalIndex}`"
                         :class="{ 
                             'preview-track': (videoTrack as any).isPreviewTrack,
                             'drag-over': dragOverTrackType === 'video' && dragOverTrackIndex === videoTrack.originalIndex
                         }">
                        <!-- 渲染所有非预览轨道的片段，包括空轨道 -->
                        <div v-if="!(videoTrack as any).isPreviewTrack && videoTrack.clips" 
                             v-for="(clip, clipIndex) in videoTrack.clips" 
                             :key="`video-${videoTrack.originalIndex}-${clipIndex}`" 
                             class="clip video-clip"
                             :style="getClipStyle(clip, 'video', videoTrack.originalIndex, clipIndex)"
                             @mousedown="startClipDrag($event, clip, 'video', videoTrack.originalIndex, clipIndex)"
                             @click="selectClip('video', videoTrack.originalIndex, clipIndex)"
                             :class="{ 
                                 selected: isSelected('video', videoTrack.originalIndex, clipIndex),
                                 'is-dragging': draggedClipInfo?.type === 'video' && draggedClipInfo?.trackIndex === videoTrack.originalIndex && draggedClipInfo?.clipIndex === clipIndex
                             }">
                            <span>{{ clip.name }}</span>
                        </div>
                        <!-- 预览轨道显示拖拽的片段 -->
                        <div v-if="(videoTrack as any).isPreviewTrack && draggedClipInfo" class="preview-clip">
                            <div class="clip video-clip preview-dragged-clip"
                                 :style="{ 
                                     left: draggedClipInfo.element?.style.left || '0px',
                                     width: draggedClipInfo.element?.style.width || '100px'
                                 }">
                                <span>拖拽中的片段</span>
                            </div>
                        </div>
                    </div>
                    <!-- 音频轨道 -->
                    <div class="track" v-for="audioTrack in displayAudioTracks" :key="`audio-track-${audioTrack.originalIndex}`"
                         :class="{ 
                             'preview-track': (audioTrack as any).isPreviewTrack,
                             'drag-over': dragOverTrackType === 'audio' && dragOverTrackIndex === audioTrack.originalIndex
                         }">
                        <div v-if="!(audioTrack as any).isPreviewTrack && audioTrack.clips" 
                             v-for="(clip, clipIndex) in audioTrack.clips" 
                             :key="`audio-${audioTrack.originalIndex}-${clipIndex}`" 
                             class="clip audio-clip"
                             :style="getClipStyle(clip, 'audio', audioTrack.originalIndex, clipIndex)"
                             @mousedown="startClipDrag($event, clip, 'audio', audioTrack.originalIndex, clipIndex)"
                             @click="selectClip('audio', audioTrack.originalIndex, clipIndex)"
                             :class="{ 
                                 selected: isSelected('audio', audioTrack.originalIndex, clipIndex),
                                 'is-dragging': draggedClipInfo?.type === 'audio' && draggedClipInfo?.trackIndex === audioTrack.originalIndex && draggedClipInfo?.clipIndex === clipIndex
                             }">
                            <span>{{ clip.name }}</span>
                        </div>
                        <div v-if="(audioTrack as any).isPreviewTrack && draggedClipInfo" class="preview-clip">
                            <div class="clip audio-clip preview-dragged-clip"
                                 :style="{ 
                                     left: draggedClipInfo.element?.style.left || '0px',
                                     width: draggedClipInfo.element?.style.width || '100px'
                                 }">
                                <span>拖拽中的片段</span>
                            </div>
                        </div>
                    </div>
                    <!-- 字幕轨道 -->
                    <div class="track" v-for="subtitleTrack in displaySubtitleTracks" :key="`subtitle-track-${subtitleTrack.originalIndex}`"
                         :class="{ 
                             'preview-track': (subtitleTrack as any).isPreviewTrack,
                             'drag-over': dragOverTrackType === 'subtitle' && dragOverTrackIndex === subtitleTrack.originalIndex
                         }">
                        <div v-if="!(subtitleTrack as any).isPreviewTrack && subtitleTrack.clips" 
                             v-for="(clip, clipIndex) in subtitleTrack.clips" 
                             :key="`subtitle-${subtitleTrack.originalIndex}-${clipIndex}`" 
                             class="clip subtitle-clip"
                             :style="getClipStyle(clip, 'subtitle', subtitleTrack.originalIndex, clipIndex)"
                             @mousedown="startClipDrag($event, clip, 'subtitle', subtitleTrack.originalIndex, clipIndex)"
                             @click="selectClip('subtitle', subtitleTrack.originalIndex, clipIndex)"
                             :class="{ 
                                 selected: isSelected('subtitle', subtitleTrack.originalIndex, clipIndex),
                                 'is-dragging': draggedClipInfo?.type === 'subtitle' && draggedClipInfo?.trackIndex === subtitleTrack.originalIndex && draggedClipInfo?.clipIndex === clipIndex
                             }">
                            <span>{{ clip.name }}</span>
                        </div>
                        <div v-if="(subtitleTrack as any).isPreviewTrack && draggedClipInfo" class="preview-clip">
                            <div class="clip subtitle-clip preview-dragged-clip"
                                 :style="{ 
                                     left: draggedClipInfo.element?.style.left || '0px',
                                     width: draggedClipInfo.element?.style.width || '100px'
                                 }">
                                <span>拖拽中的片段</span>
                            </div>
                        </div>
                    </div>                    <!-- 播放头垂直线 - 贯穿所有轨道 -->
                    <div class="playhead-line" :style="{ 
                        left: `${playheadLeft}px`, 
                        height: `${totalTracksCount * 61}px`
                    }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, type ComputedRef } from 'vue'
import { ElIcon } from 'element-plus'
import { VideoPlay, VideoPause, RefreshLeft, Scissor, Delete } from '@element-plus/icons-vue'
import { useVideoEditorStore } from '../useVideoEditor'
import { formatRulerTime } from '../../utils/timeUtils'
import { 
    smartTrackDragCalculation,
    addPreviewTrackToDisplayTracks,
    processTrackData,
    type DragCalculationResult
} from '../../utils/timelineUtils'
import { 
    createDragManager,
    calculateDragPosition,
    applySnapping,
    type TrackLayout,
    type DragPosition
} from '../../utils/dragUtils'
import type { Timeline, TimelineClip, RulerMarker, ProcessedClip, DraggedClipData } from '../../types/videoEdit';

const videoEditorStore = useVideoEditorStore();

const rulerScrollContainerRef = ref<HTMLElement | null>(null);
const mainScrollAreaRef = ref<HTMLElement | null>(null);
const tracksContentAreaRef = ref<HTMLElement | null>(null);
const rulerRef = ref<HTMLElement | null>(null);

const PIXELS_PER_SECOND = 50;
const SNAPPING_THRESHOLD_PX = 8;
const DRAG_THRESHOLD_PX = 3;
const TRACK_HEIGHT = 61;
const CROSS_TRACK_THRESHOLD = 5;

// ======================= 拖拽管理器 =======================
const dragManager = createDragManager();

// 直接使用拖拽管理器的响应式状态
const isDragging = dragManager.isDragging;
const draggedClipInfo = dragManager.draggedClip;
const dragOverTrackType = computed(() => dragManager.dragOverTrack.type);
const dragOverTrackIndex = computed(() => dragManager.dragOverTrack.index);
const isShowingPreviewTrack = dragManager.isShowingPreviewTrack;

// 计算属性
const rulerMarkers: ComputedRef<RulerMarker[]> = computed(() => {
    if (videoEditorStore.videoDurationSeconds <= 0) return [];
    const markers: RulerMarker[] = [];
    const totalDurationInSeconds = Math.ceil(videoEditorStore.videoDurationSeconds);
    const totalMarkers = totalDurationInSeconds + 20;

    for (let i = 0; i < totalMarkers; i++) {
        markers.push({
            second: i,
            showLabel: i % 5 === 0
        });
    }
    return markers;
});

// 多轨道计算属性 - 使用工具函数简化处理
const allVideoTracks = computed(() => {
    // **重要**: 确保不修改原始数据，使用深拷贝
    const originalTimeline = videoEditorStore.timeline;
    if (!originalTimeline?.VideoTracks) {
        return [];
    }
    
    // 从VideoTracks中筛选出Type为"Video"的轨道（排除Subtitle类型）
    const allTracks = originalTimeline.VideoTracks || [];
    const rawTracks = allTracks.filter(track => track.Type === 'Video' || !track.Type); // 兼容没有Type字段的情况
    
    // 检查每个轨道的结构并修正clips属性名
    const normalizedTracks = rawTracks.map((track, index) => {
        // 获取正确的clips数组 - 根据实际数据结构
        let clips = [];
        if (track.VideoTrackClips) {
            clips = track.VideoTrackClips;
        } else if (track.clips) {
            clips = track.clips;
        }
        
        // 返回标准化的轨道对象
        return {
            ...track,
            clips: clips
        };
    });
    
    const clipMapper = (clip: any) => {
        // 兼容不同的属性命名方式（大写或小写）
        const timelineIn = clip.TimelineIn ?? clip.timelineIn ?? 0;
        const timelineOut = clip.TimelineOut ?? clip.timelineOut ?? 0;
        
        return {
            name: clip.Title || clip.title || clip.FileName || clip.fileName || clip.MediaId || clip.mediaId || '未命名',
            start: timelineIn,
            duration: timelineOut - timelineIn,
        };
    };
    
    const processedTracks = processTrackData(normalizedTracks, clipMapper);
    return processedTracks;
});

// 存储最新的拖拽结果
const currentDragResult = ref<DragCalculationResult | null>(null);

// 显示轨道计算（包含预览轨道逻辑）
const displayVideoTracks = computed(() => {
    const tracks = allVideoTracks.value;
    // 直接使用轨道数据，不创建默认轨道
    
    // 使用实时的拖拽结果
    const dragResult = currentDragResult.value || {
        targetTrackIndex: dragOverTrackIndex.value,
        isValidDrop: dragOverTrackIndex.value !== null,
        isNewTrack: isShowingPreviewTrack.value,
        insertPosition: 'replace' as const,
        snapZone: 'center' as const
    };
    
    return addPreviewTrackToDisplayTracks(
        tracks, 
        'video', 
        dragResult, 
        isShowingPreviewTrack.value && draggedClipInfo.value?.type === 'video'
    );
});

const allAudioTracks = computed(() => {
    const rawTracks = videoEditorStore.timeline?.AudioTracks || [];
    
    // 标准化轨道数据结构
    const normalizedTracks = rawTracks.map((track, index) => {
        let clips = [];
        if (track.AudioTrackClips) {
            clips = track.AudioTrackClips;
        } else if (track.clips) {
            clips = track.clips;
        }
        
        return {
            ...track,
            clips: clips
        };
    });
    
    const clipMapper = (clip: any) => {
        // 兼容不同的属性命名方式（大写或小写）
        const timelineIn = clip.TimelineIn ?? clip.timelineIn ?? 0;
        const timelineOut = clip.TimelineOut ?? clip.timelineOut ?? 0;
        
        return {
            name: clip.Title || clip.title || clip.FileName || clip.fileName || clip.MediaId || clip.mediaId || '未命名',
            start: timelineIn,
            duration: timelineOut - timelineIn,
        };
    };
    const processedTracks = processTrackData(normalizedTracks, clipMapper);
    return processedTracks;
});

const displayAudioTracks = computed(() => {
    const tracks = allAudioTracks.value;
    // 直接使用轨道数据，不创建默认轨道
    
    // 使用实时的拖拽结果
    const dragResult = currentDragResult.value || {
        targetTrackIndex: dragOverTrackIndex.value,
        isValidDrop: dragOverTrackIndex.value !== null,
        isNewTrack: isShowingPreviewTrack.value,
        insertPosition: 'replace' as const,
        snapZone: 'center' as const
    };
    
    return addPreviewTrackToDisplayTracks(
        tracks, 
        'audio', 
        dragResult, 
        isShowingPreviewTrack.value && draggedClipInfo.value?.type === 'audio'
    );
});

const allSubtitleTracks = computed(() => {
    // 从VideoTracks中筛选出Type为"Subtitle"的轨道
    const allTracks = videoEditorStore.timeline?.VideoTracks || [];
    const rawTracks = allTracks.filter(track => track.Type === 'Subtitle');
    
    // 标准化轨道数据结构 - 字幕轨道的clips在VideoTrackClips中
    const normalizedTracks = rawTracks.map((track, index) => {
        let clips = [];
        if (track.VideoTrackClips) {
            clips = track.VideoTrackClips;
        } else if (track.clips) {
            clips = track.clips;
        }
        
        return {
            ...track,
            clips: clips
        };
    });
    
    const clipMapper = (clip: any) => {
        // 兼容不同的属性命名方式（大写或小写）
        const timelineIn = clip.TimelineIn ?? clip.timelineIn ?? 0;
        const timelineOut = clip.TimelineOut ?? clip.timelineOut ?? 0;
        
        return {
            name: clip.Content || clip.content || clip.Title || clip.title || '字幕',
            start: timelineIn,
            duration: timelineOut - timelineIn,
        };
    };
    const processedTracks = processTrackData(normalizedTracks, clipMapper);
    return processedTracks;
});

const displaySubtitleTracks = computed(() => {
    const tracks = allSubtitleTracks.value;
    // 直接使用轨道数据，不创建默认轨道
    
    // 使用实时的拖拽结果
    const dragResult = currentDragResult.value || {
        targetTrackIndex: dragOverTrackIndex.value,
        isValidDrop: dragOverTrackIndex.value !== null,
        isNewTrack: isShowingPreviewTrack.value,
        insertPosition: 'replace' as const,
        snapZone: 'center' as const
    };
    
    return addPreviewTrackToDisplayTracks(
        tracks, 
        'subtitle', 
        dragResult, 
        isShowingPreviewTrack.value && draggedClipInfo.value?.type === 'subtitle'
    );
});

const playheadLeft = computed<number>(() => {
    return videoEditorStore.currentTime * PIXELS_PER_SECOND;
});

const canCut = computed(() => {
    return videoEditorStore.selectedClip.type !== null && 
           videoEditorStore.selectedClip.trackIndex !== null &&
           videoEditorStore.selectedClip.clipIndex !== null;
});

const canDelete = computed(() => {
    return videoEditorStore.selectedClip.type !== null && 
           videoEditorStore.selectedClip.trackIndex !== null &&
           videoEditorStore.selectedClip.clipIndex !== null;
});

// 计算总轨道数用于播放头高度
const totalTracksCount = computed(() => {
    return displayVideoTracks.value.length + displayAudioTracks.value.length + displaySubtitleTracks.value.length;
});

// ======================= 简化拖拽函数 =======================

// 开始拖拽片段
const startClipDrag = (event: MouseEvent, clip: ProcessedClip, type: 'video' | 'audio' | 'subtitle', trackIndex: number, clipIndex: number) => {
    event.preventDefault();
    event.stopPropagation();
    
    const element = event.currentTarget as HTMLElement;
    const rect = element.getBoundingClientRect();
    const containerRect = tracksContentAreaRef.value?.getBoundingClientRect();
    if (!containerRect) return;
    
    // 计算鼠标相对于片段的偏移
    const offsetX = event.clientX - rect.left;
    
    // 使用拖拽管理器开始拖拽
    dragManager.startDrag({
        type,
        trackIndex, 
        clipIndex,
        element,
        initialLeft: rect.left - containerRect.left + (mainScrollAreaRef.value?.scrollLeft || 0),
        offsetX
    });
    
    // 添加拖拽样式（无动画效果）
    element.style.zIndex = '1000';
    element.style.pointerEvents = 'none';
    
    if (videoEditorStore.isVideoPlaying) {
        videoEditorStore.handlePause();
    }
    
    // 添加全局事件监听
    document.addEventListener('mousemove', handleClipDrag);
    document.addEventListener('mouseup', endClipDrag);
};

// 处理片段拖拽
const handleClipDrag = (event: MouseEvent) => {
    if (!isDragging.value || !draggedClipInfo.value || !tracksContentAreaRef.value || !mainScrollAreaRef.value) return;
    
    const containerRect = tracksContentAreaRef.value.getBoundingClientRect();
    const scrollLeft = mainScrollAreaRef.value.scrollLeft;
    const scrollTop = mainScrollAreaRef.value.scrollTop;
    const scrollAreaRect = mainScrollAreaRef.value.getBoundingClientRect();
    
    // 计算片段的新X位置
    const newX = Math.max(0, event.clientX - containerRect.left + scrollLeft - draggedClipInfo.value.offsetX);
    
    // 更新片段位置（无动画）
    draggedClipInfo.value.element.style.left = `${newX}px`;
    
    // 使用智能轨道计算
    const dragResult = smartTrackDragCalculation({
        mouseY: event.clientY,
        trackType: draggedClipInfo.value.type,
        scrollAreaRect,
        scrollTop,
        trackHeight: TRACK_HEIGHT,
        displayTracks: getDisplayTracksForType(draggedClipInfo.value.type),
        allTracks: getAllTracksForType(draggedClipInfo.value.type),
        sourceTrackIndex: draggedClipInfo.value.trackIndex,
        isDraggingFromLibrary: false
    });
    
    // 存储拖拽结果供computed使用
    currentDragResult.value = dragResult;
    
    // 构建简化的位置对象供拖拽管理器使用
    const position: DragPosition = {
        x: newX,
        y: event.clientY - scrollAreaRect.top + scrollTop,
        trackIndex: dragResult.targetTrackIndex || -1,
        trackType: dragResult.isValidDrop ? draggedClipInfo.value.type : null,
        targetTrackIndex: dragResult.targetTrackIndex
    };
    
    // 更新拖拽管理器状态
    const trackCounts = {
        video: allVideoTracks.value.length,
        audio: allAudioTracks.value.length,
        subtitle: allSubtitleTracks.value.length
    };
    
    dragManager.updatePosition(position, trackCounts);
};

// 辅助函数：根据类型获取对应的显示轨道数组
const getDisplayTracksForType = (type: 'video' | 'audio' | 'subtitle') => {
    switch (type) {
        case 'video': return displayVideoTracks.value;
        case 'audio': return displayAudioTracks.value;
        case 'subtitle': return displaySubtitleTracks.value;
        default: return [];
    }
};

// 辅助函数：根据类型获取对应的所有轨道数组
const getAllTracksForType = (type: 'video' | 'audio' | 'subtitle') => {
    switch (type) {
        case 'video': return allVideoTracks.value;
        case 'audio': return allAudioTracks.value;
        case 'subtitle': return allSubtitleTracks.value;
        default: return [];
    }
};

// 结束片段拖拽
const endClipDrag = (event: MouseEvent) => {
    if (!isDragging.value || !draggedClipInfo.value) return;
    
    const element = draggedClipInfo.value.element;
    const finalLeft = parseFloat(element.style.left || '0');
    const newStartTime = Math.max(0, finalLeft / PIXELS_PER_SECOND);
    
    // 应用吸附逻辑
    const snappedTime = applySnapping(
        newStartTime,
        videoEditorStore.timeline,
        draggedClipInfo.value,
        PIXELS_PER_SECOND,
        SNAPPING_THRESHOLD_PX / PIXELS_PER_SECOND
    );
    
    // 使用智能拖拽结果判断操作类型
    const dragResult = currentDragResult.value;
    const isCrossTrackMove = dragResult && 
                            dragResult.isValidDrop && 
                            dragResult.targetTrackIndex !== null && 
                            (dragResult.isNewTrack || dragResult.targetTrackIndex !== draggedClipInfo.value.trackIndex);
    
    if (isCrossTrackMove && dragResult) {
        // 执行跨轨道移动或新轨道创建
        videoEditorStore.moveClipToTrackAction(
            draggedClipInfo.value.type,
            draggedClipInfo.value.trackIndex,
            draggedClipInfo.value.clipIndex,
            dragResult.targetTrackIndex!,
            snappedTime
        );
    } else {
        // 执行普通的时间更新
        videoEditorStore.updateClipTime(
            draggedClipInfo.value.type,
            draggedClipInfo.value.trackIndex,
            draggedClipInfo.value.clipIndex,
            snappedTime
        );
    }
    
    // 清理状态
    element.style.zIndex = '';
    element.style.pointerEvents = '';
    
    // 清理拖拽结果状态
    currentDragResult.value = null;
    
    // 使用拖拽管理器重置状态
    dragManager.reset();
    
    document.removeEventListener('mousemove', handleClipDrag);
    document.removeEventListener('mouseup', endClipDrag);
};

// 时间轴交互
const handleTimelineInteraction = (event: MouseEvent) => {
    if (isDragging.value || !rulerScrollContainerRef.value || !rulerRef.value) return;

    if (videoEditorStore.isVideoPlaying) {
        videoEditorStore.handlePause();
    }

    event.preventDefault();

    const seekToEventTime = (e: MouseEvent) => {
        // 使用标尺容器的位置和滚动信息来精确计算位置
        const containerRect = rulerScrollContainerRef.value!.getBoundingClientRect();
        const rulerRect = rulerRef.value!.getBoundingClientRect();
        const scrollLeft = rulerScrollContainerRef.value!.scrollLeft;
        
        // 计算相对于标尺容器的偏移量
        const offsetX = e.clientX - containerRect.left + scrollLeft;
        
        let timeInSeconds = offsetX / PIXELS_PER_SECOND;

        timeInSeconds = Math.max(0, timeInSeconds);
        if (videoEditorStore.videoDurationSeconds > 0) {
            timeInSeconds = Math.min(timeInSeconds, videoEditorStore.videoDurationSeconds);
        }
        
        videoEditorStore.handleSeek(timeInSeconds);
    };

    seekToEventTime(event);

    const handleMouseMove = (e: MouseEvent) => {
        e.preventDefault();
        seekToEventTime(e);
    };

    const handleMouseUp = () => {
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
};

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  
  // 核心：精确同步标尺和轨道内容的水平滚动
  if (rulerScrollContainerRef.value && target !== rulerScrollContainerRef.value) {
    rulerScrollContainerRef.value.scrollLeft = target.scrollLeft;
  }
};

// 处理标尺区域滚动，同步到轨道内容区域
const handleRulerScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  
  // 从标尺滚动同步到轨道内容
  if (mainScrollAreaRef.value && target !== mainScrollAreaRef.value) {
    mainScrollAreaRef.value.scrollLeft = target.scrollLeft;
  }
};

const selectClip = (type: 'video' | 'audio' | 'subtitle', trackIndex: number, clipIndex: number) => {
    videoEditorStore.setSelectedClip({ type, trackIndex, clipIndex });
}

const isSelected = (type: 'video' | 'audio' | 'subtitle', trackIndex: number, clipIndex: number) => {
    return videoEditorStore.selectedClip.type === type && 
           videoEditorStore.selectedClip.trackIndex === trackIndex && 
           videoEditorStore.selectedClip.clipIndex === clipIndex;
}

const getClipStyle = (clip: ProcessedClip, type?: 'video' | 'audio' | 'subtitle', trackIndex?: number, clipIndex?: number) => {
    const baseStyle = {
        left: `${clip.start * PIXELS_PER_SECOND}px`,
        width: `${clip.duration * PIXELS_PER_SECOND}px`
    };
    
    // 在简化版本中，不再做预览位置计算，直接返回基础样式
    return baseStyle;
}
</script>

<style lang="scss" scoped>
.timeline-component {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #1e1e1e;
    color: #e0e0e0;
    overflow: hidden;
}

.timeline-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 12px;
    background-color: #252526;
    border-bottom: 1px solid #333;

    .controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    button {
        background: none;
        border: none;
        color: #ccc;
        font-size: 20px;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        justify-content: center;
        align-items: center;

        &:hover {
            background-color: #3e3e42;
            color: #fff;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            
            &:hover {
                background-color: transparent;
                color: #ccc;
            }
        }
    }

    .time-display {
        font-family: monospace;
        font-size: 12px;
        color: #ccc;
        margin-left: 12px;
    }
}

.timeline-body {
    flex-grow: 1;
    display: grid;
    grid-template-columns: 120px 1fr; /* 固定轨道标签列宽度，内容列自适应 */
    grid-template-rows: 30px 1fr; /* 标尺固定高度，轨道区域自适应 */
    overflow: hidden;
    position: relative;
}

/* 左上角固定区域 */
.timeline-corner {
    grid-column: 1;
    grid-row: 1;
    background-color: #333435;
    border-right: 1px solid #444;
    border-bottom: 1px solid #333;
    z-index: 6;

    .corner-placeholder {
        width: 100%;
        height: 100%;
    }
}

/* 时间标尺区域 - 固定在顶部，只水平滚动 */
.timeline-ruler-section {
    grid-column: 2;
    grid-row: 1;
    background-color: #252526;
    border-bottom: 1px solid #333;
    overflow: hidden;
    position: relative;
    z-index: 5;

    .ruler-scroll-container {
        width: 100%;
        height: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        
        /* 隐藏滚动条但保持滚动能力 */
        scrollbar-width: none;
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .ruler {
        height: 30px;
        display: flex;
        align-items: flex-end;
        width: fit-content;
        cursor: pointer;
        position: relative;

        .ruler-mark {
            flex-shrink: 0;
            width: 50px;
            border-left: 1px solid #444;
            padding-bottom: 4px;
            padding-left: 4px;
            font-size: 10px;
            color: #888;

            .time {
                font-family: monospace;
                font-size: 10px;
                color: #ccc;
            }
        }
    }

    .playhead-cursor {
        position: absolute;
        top: 0;
        width: 2px;
        height: 30px;
        pointer-events: none;
        z-index: 6;
        transform: translateX(-1px);

        .playhead-top {
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 8px 6px 0 6px;
            border-color: #e53935 transparent transparent transparent;
        }
    }
}

/* 主滚动区域 - 跨越左下和右下 */
.timeline-main-scroll-area {
    grid-column: 1 / 3;
    grid-row: 2;
    display: flex;
    overflow: auto;
    min-height: 0;
    
    /* 滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #555 #2a2d2e;
    
    &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    &::-webkit-scrollbar-track {
        background: #2a2d2e;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 4px;
        
        &:hover {
            background: #666;
        }
    }
}

/* 轨道标签列 - 固定宽度，水平位置固定，垂直滚动跟随 */
.track-labels-column {
    position: sticky;
    left: 0;
    z-index: 3;
    flex-shrink: 0;
    width: 120px;
    background-color: #333435;
    border-right: 1px solid #444;
    min-height: fit-content;

    .track-header {
        height: 60px;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 10px;
        font-size: 12px;
        font-weight: 500;
        border-bottom: 1px solid #3a3a3a;
        flex-shrink: 0;
        
        .track-icon {
            width: 16px;
            height: 16px;
            color: #888;
        }
        
        span {
            color: #ccc;
            user-select: none;
        }
    }
}

/* 轨道内容区域 - 水平和垂直滚动 */
.tracks-content-area {
    flex-grow: 1;
    position: relative;
    min-height: fit-content;

    .track {
        height: 60px;
        position: relative;
        border-bottom: 1px solid #3a3a3a;
        background-color: #2a2d2e;
        flex-shrink: 0;
        
        &.drag-over {
            background-color: rgba(64, 158, 255, 0.15);
            border: 2px dashed rgba(64, 158, 255, 0.6);
            border-radius: 4px;
            animation: drag-over-pulse 1s ease-in-out infinite;
        }
        
        .clip {
            position: absolute;
            top: 8px;
            height: 44px;
            border-radius: 4px;
            cursor: grab;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            padding: 0 8px;
            user-select: none;
            
            &:active {
                cursor: grabbing;
            }
            
            span {
                color: white;
                font-size: 11px;
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            &.video-clip {
                background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            }
            
            &.audio-clip {
                background: linear-gradient(135deg, #8e44ad 0%, #732d91 100%);
            }
            
            &.subtitle-clip {
                background: linear-gradient(135deg, #27ae60 0%, #219a52 100%);
            }
            
            &.selected {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
            }
            
            &.is-dragging {
                z-index: 1000;
                border-color: #409eff;
            }
            
            &.preview-dragged-clip {
                opacity: 0.8;
                border: 2px solid rgba(64, 158, 255, 0.8);
                box-shadow: 0 0 8px rgba(64, 158, 255, 0.4);
            }
        }

        &.preview-track {
            background: rgba(64, 158, 255, 0.1);
            border: 2px dashed rgba(64, 158, 255, 0.5);
            border-radius: 4px;
        }
    }

    .preview-clip {
        position: relative;
        width: 100%;
        height: 100%;
    }
    }

    .playhead-line {
        position: absolute;
        top: 0;
        width: 2px;
        background-color: #e53935;
        pointer-events: none;
        z-index: 4;
        transform: translateX(-1px);
    }


@keyframes drag-over-pulse {
    0%, 100% {
        background-color: rgba(64, 158, 255, 0.1);
        border-color: rgba(64, 158, 255, 0.4);
    }
    50% {
        background-color: rgba(64, 158, 255, 0.2);
        border-color: rgba(64, 158, 255, 0.7);
    }
}
</style>