<template>
  <component
    :is="type"
    ref="mediaRef"
    v-bind="mediaAttrs"
    :controls="controls"
    :poster="poster"
    :preload="preload"
    class="lazy-media"
  >
    <source v-if="realSrc" :src="realSrc" :type="mimeType" />
    <slot />
  </component>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, inject } from 'vue'

const props = defineProps({
  src: { type: String, required: true },
  type: { type: String, default: 'video' },
  controls: { type: Boolean, default: true },
  poster: String,
  preload: { type: String, default: 'metadata' },
  mimeType: String,
  mediaAttrs: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['media-loaded', 'loading-start', 'loading-complete'])

const realSrc = ref('')
const mediaRef = ref(null)
let observer = null
let isLoading = false // 防止多个加载请求同时触发

// 从父组件注入已加载媒体URL集合
const loadedMediaInfo = inject('loadedMediaInfo', ref({
  baseUrls: new Set(), // 存储基础URL（不含签名参数）
  fullUrls: new Map()  // 映射基础URL到完整URL
}))

// 注入全局唯一标识符生成器
const componentId = ref(`lazy-media-${Date.now()}-${Math.floor(Math.random() * 1000)}`)

// 从完整URL中提取基础URL（去除查询参数）
const getBaseUrl = (url) => {
  if (!url) return ''
  try {
    const parsedUrl = new URL(url)
    // 只保留路径部分，去除查询参数和哈希
    return parsedUrl.origin + parsedUrl.pathname
  } catch (e) {
    console.error('解析URL失败:', e)
    return url
  }
}

console.log(`组件初始化: ${componentId.value}, src: ${props.src}`)

const loadMedia = () => {
  // 如果正在加载或者已经加载过，则不执行任何操作
  if (isLoading || loadedOnce.value) {
    console.log(`组件${componentId.value}跳过加载: isLoading=${isLoading}, loadedOnce=${loadedOnce.value}`)
    return
  }

  const baseUrl = getBaseUrl(props.src)

  // 检查全局已加载集合
  if (loadedMediaInfo.value && loadedMediaInfo.value.baseUrls.has(baseUrl)) {
    // 使用已加载的完整URL，而不是当前props中的URL
    const loadedFullUrl = loadedMediaInfo.value.fullUrls.get(baseUrl)
    console.log(`组件${componentId.value}发现全局已加载基础URL: ${baseUrl}, 使用完整URL: ${loadedFullUrl}`)
    realSrc.value = loadedFullUrl
    loadedOnce.value = true
    emit('media-loaded', { src: loadedFullUrl, type: props.type })
    return
  }

  console.log(`组件${componentId.value}开始加载: ${props.src}, 基础URL: ${baseUrl}`)
  isLoading = true
  emit('loading-start', { src: props.src, type: props.type })

  // 设置实际的src
  realSrc.value = props.src
  loadedOnce.value = true

  // 记录已加载的基础URL和完整URL到全局状态
  if (loadedMediaInfo.value) {
    loadedMediaInfo.value.baseUrls.add(baseUrl)
    loadedMediaInfo.value.fullUrls.set(baseUrl, props.src)
  }

  isLoading = false

  console.log(`组件${componentId.value}加载完成: ${props.src}, 基础URL: ${baseUrl}`)
  emit('media-loaded', { src: props.src, type: props.type })
  emit('loading-complete', { src: props.src, type: props.type })
}

const loadedOnce = ref(false)

// 监听src变化，重置加载状态
watch(() => props.src, (newSrc, oldSrc) => {
  console.log(`组件${componentId.value}检测到src变化: ${oldSrc} -> ${newSrc}`)

  if (newSrc !== oldSrc && newSrc) {
    // 重置加载状态
    loadedOnce.value = false
    isLoading = false
    realSrc.value = '' // 清空当前src

    const newBaseUrl = getBaseUrl(newSrc)

    // 如果新的src基础URL已在全局集合中，直接设置
    if (loadedMediaInfo.value && loadedMediaInfo.value.baseUrls.has(newBaseUrl)) {
      const loadedFullUrl = loadedMediaInfo.value.fullUrls.get(newBaseUrl)
      console.log(`组件${componentId.value}新src基础URL已全局加载: ${newBaseUrl}, 使用完整URL: ${loadedFullUrl}`)
      realSrc.value = loadedFullUrl
      loadedOnce.value = true
    } else {
      // 否则如果元素在视口内，立即加载
      console.log(`组件${componentId.value}准备加载新src: ${newSrc}, 基础URL: ${newBaseUrl}`)
      // 这里不直接调用loadMedia，而是让IntersectionObserver来处理
    }
  }
})

onMounted(() => {
  console.log(`组件${componentId.value}挂载完成，准备初始化观察者`)

  // 检查是否已经全局加载过
  const baseUrl = getBaseUrl(props.src)
  if (loadedMediaInfo.value && loadedMediaInfo.value.baseUrls.has(baseUrl)) {
    const loadedFullUrl = loadedMediaInfo.value.fullUrls.get(baseUrl)
    console.log(`组件${componentId.value}挂载时发现已全局加载: ${baseUrl}`)
    realSrc.value = loadedFullUrl
    loadedOnce.value = true
    return // 已加载过，不需要观察者
  }

  // 如果还未加载，设置观察者
  if (mediaRef.value) {
    // 先清理可能存在的旧观察者（防止重复创建）
    if (observer) {
      observer.disconnect()
      observer = null
    }

    observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        console.log(`组件${componentId.value}观察者触发: isIntersecting=${entry.isIntersecting}, loadedOnce=${loadedOnce.value}`)

        if (entry.isIntersecting && !loadedOnce.value && !isLoading) {
          console.log(`组件${componentId.value}视口可见，开始加载`)
          loadMedia()

          // 加载完成后立即停止观察
          if (observer) {
            console.log(`组件${componentId.value}停止观察`)
            observer.disconnect()
            observer = null
          }
        }
      },
      {
        threshold: 0.1, // 元素至少10%可见时触发，降低阈值减少重复触发
        rootMargin: '50px' // 减少提前加载距离，避免过早触发
      }
    )

    console.log(`组件${componentId.value}开始观察元素`)
    observer.observe(mediaRef.value)
  } else {
    console.error(`组件${componentId.value}挂载时未找到媒体元素`)
  }
})

onBeforeUnmount(() => {
  console.log(`组件${componentId.value}即将卸载，清理观察者`)
  
  if (observer) {
    observer.disconnect()
    observer = null
  }
})
</script>

<style scoped>
.lazy-media {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background: #000;
}
</style>