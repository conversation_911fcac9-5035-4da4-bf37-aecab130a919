<script setup>
import { useRoute } from 'vue-router'
import { ref, computed, onMounted, nextTick } from 'vue'
import { Search, ArrowUp, ArrowDown, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

import { getTingwuTrans, updateTingwuTrans } from '@/api/tingwu/record'
import { getUrlByAudioPath } from '@/api/platform/audio'

import AudioPlayer from './components/AudioPlayer.vue'
import KeywordsSection from './components/KeywordsSection.vue'
import ConversationList from './components/ConversationList.vue'
import RightPanel from './components/RightPanel.vue'
import ExportDialog from './components/ExportDialog.vue'

const route = useRoute()
const recordId = route.params.vaId;
const record = ref(null)
const audioUrl = ref("")
const audioPlayerRef = ref(null) // AudioPlayer组件引用
const currentPlayingIndex = ref(-1) // 当前播放的对话索引
const currentPlayingTime = ref(0) // 当前播放时间（毫秒）

// 搜索相关状态
const showSearchBox = ref(false) // 控制搜索框显示
const searchKeyword = ref('') // 搜索关键词
const searchResults = ref([]) // 搜索结果
const currentSearchIndex = ref(-1) // 当前搜索结果索引

// 格式化对话内容 - 处理新的数据格式
const formattedConversations = computed(() => {
  if (!record.value || !record.value.paragraphs) return []
  try {
    const paragraphsData = JSON.parse(record.value.paragraphs)
    console.log('paragraphsData>>>>', paragraphsData)
    return paragraphsData
  } catch (error) {
    console.error('解析对话内容失败:', error)
    return []
  }
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 点击对话item跳转到对应音频位置
const handleConversationClick = (conversation) => {
  if (audioPlayerRef.value && conversation.startTime) {
    // 跳转到对话开始时间
    audioPlayerRef.value.seekToTime(conversation.startTime)

    // 提供视觉反馈
    console.log(`跳转到音频位置: ${formatTimestamp(conversation.startTime)}`)
  }
}

// 根据当前播放时间更新高亮的对话
const updateCurrentPlayingConversation = (currentTimeMs) => {
  const conversations = formattedConversations.value
  let foundIndex = -1

  for (let i = 0; i < conversations.length; i++) {
    const conversation = conversations[i]
    if (currentTimeMs >= conversation.startTime && currentTimeMs <= conversation.endTime) {
      foundIndex = i
      break
    }
  }

  const previousIndex = currentPlayingIndex.value
  currentPlayingIndex.value = foundIndex

  // 如果当前播放的对话改变了，自动滚动到该对话
  if (foundIndex !== -1 && foundIndex !== previousIndex) {
    scrollToConversation(foundIndex)
  }
}

// 滚动到指定的对话
const scrollToConversation = (index) => {
  nextTick(() => {
    const conversationElement = document.querySelector(`.conversation-item:nth-child(${index + 1})`)
    if (conversationElement) {
      conversationElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  })
}

// 监听音频播放时间变化
const handleAudioTimeUpdate = (currentTimeSeconds) => {
  const currentTimeMs = currentTimeSeconds * 1000
  currentPlayingTime.value = currentTimeMs // 更新当前播放时间
  updateCurrentPlayingConversation(currentTimeMs)
}

// 搜索相关方法
const toggleSearch = () => {
  showSearchBox.value = !showSearchBox.value
  if (!showSearchBox.value) {
    clearSearch()
  }
}

const performSearch = () => {
  if (!searchKeyword.value.trim()) {
    clearSearch()
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const results = []

  formattedConversations.value.forEach((conversation, index) => {
    if (conversation.text.toLowerCase().includes(keyword)) {
      results.push({
        conversationIndex: index,
        conversation: conversation
      })
    }
  })

  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1

  // 如果有搜索结果，滚动到第一个结果
  if (results.length > 0) {
    scrollToSearchResult(0)
  }
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1
}

const scrollToSearchResult = (index) => {
  if (index >= 0 && index < searchResults.value.length) {
    const conversationIndex = searchResults.value[index].conversationIndex
    scrollToConversation(conversationIndex)
  }
}

const nextSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
    scrollToSearchResult(currentSearchIndex.value)
  }
}

const prevSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = currentSearchIndex.value <= 0
      ? searchResults.value.length - 1
      : currentSearchIndex.value - 1
    scrollToSearchResult(currentSearchIndex.value)
  }
}

// 智能分配新文本到词汇详情
const distributeTextToWordDetails = (newText, wordDetails) => {
  if (!wordDetails || !Array.isArray(wordDetails) || wordDetails.length === 0) {
    return wordDetails
  }

  // 移除标点符号和空格，用于比较
  const cleanNewText = newText.replace(/[^\u4e00-\u9fa5\w]/g, '')
  const originalWords = wordDetails.map(word => word.text.replace(/[^\u4e00-\u9fa5\w]/g, ''))
  const originalCleanText = originalWords.join('')

  console.log('文本分配调试信息:')
  console.log('原始文本:', originalCleanText)
  console.log('新文本:', cleanNewText)
  console.log('原始词汇数量:', wordDetails.length)

  // 如果清理后的文本长度相同，尝试按字符对应分配
  if (cleanNewText.length === originalCleanText.length) {
    console.log('长度相同，使用字符对应分配')
    // 逐字符对应分配
    let newTextIndex = 0
    const updatedWordDetails = wordDetails.map((word, index) => {
      const cleanWordText = word.text.replace(/[^\u4e00-\u9fa5\w]/g, '')
      const wordLength = cleanWordText.length

      if (newTextIndex + wordLength <= cleanNewText.length) {
        const newWordText = cleanNewText.substring(newTextIndex, newTextIndex + wordLength)
        newTextIndex += wordLength

        // 保持原有的标点符号格式
        let finalText = newWordText
        if (word.text.match(/[^\u4e00-\u9fa5\w]/)) {
          // 如果原文本有标点符号，尝试保持
          const punctuation = word.text.replace(/[\u4e00-\u9fa5\w]/g, '')
          finalText = newWordText + punctuation
        }

        console.log(`词汇 ${index}: "${word.text}" -> "${finalText}"`)

        return {
          ...word,
          text: finalText
        }
      }

      return word
    })

    return updatedWordDetails
  }

  // 如果长度不同，使用更智能的分配策略
  console.log('长度不同，使用比例分配')
  return distributeTextByLength(newText, wordDetails)
}

// 按长度比例分配文本到词汇详情
const distributeTextByLength = (newText, wordDetails) => {
  const totalOriginalLength = wordDetails.reduce((sum, word) => {
    return sum + word.text.replace(/[^\u4e00-\u9fa5\w]/g, '').length
  }, 0)

  if (totalOriginalLength === 0) {
    return wordDetails
  }

  const cleanNewText = newText.replace(/[^\u4e00-\u9fa5\w]/g, '')
  let currentIndex = 0

  console.log('比例分配详情:')
  console.log('原始总长度:', totalOriginalLength)
  console.log('新文本长度:', cleanNewText.length)

  const updatedWordDetails = wordDetails.map((word, index) => {
    const cleanWordText = word.text.replace(/[^\u4e00-\u9fa5\w]/g, '')
    const wordLength = cleanWordText.length

    // 计算这个词汇应该分配的新文本长度
    let newWordLength
    if (index === wordDetails.length - 1) {
      // 最后一个词汇，分配剩余的所有字符
      newWordLength = cleanNewText.length - currentIndex
    } else {
      // 按比例分配
      const ratio = wordLength / totalOriginalLength
      newWordLength = Math.max(1, Math.round(cleanNewText.length * ratio))
    }

    // 确保不超出边界
    newWordLength = Math.min(newWordLength, cleanNewText.length - currentIndex)

    if (newWordLength > 0 && currentIndex < cleanNewText.length) {
      const newWordText = cleanNewText.substring(currentIndex, currentIndex + newWordLength)
      currentIndex += newWordLength

      // 保持原有的标点符号格式
      let finalText = newWordText
      if (word.text.match(/[^\u4e00-\u9fa5\w]/)) {
        const punctuation = word.text.replace(/[\u4e00-\u9fa5\w]/g, '')
        finalText = newWordText + punctuation
      }

      console.log(`词汇 ${index}: "${word.text}" (长度${wordLength}) -> "${finalText}" (分配长度${newWordLength})`)

      return {
        ...word,
        text: finalText
      }
    }

    // 如果没有更多字符可分配，保持原文本
    console.log(`词汇 ${index}: "${word.text}" -> 保持原文本 (无更多字符可分配)`)
    return word
  })

  return updatedWordDetails
}

// 处理文本更新
const handleTextUpdate = async (updateData) => {
  try {
    // 解析当前的 paragraphs 数据
    const paragraphsData = JSON.parse(record.value.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      throw new Error('paragraphs 数据格式不正确，应该是数组')
    }

    // 验证索引有效性
    if (updateData.index < 0 || updateData.index >= paragraphsData.length) {
      throw new Error(`索引 ${updateData.index} 超出范围，数组长度为 ${paragraphsData.length}`)
    }

    // 更新指定索引的文本内容
    const targetItem = paragraphsData[updateData.index]
    if (!targetItem) {
      throw new Error(`无法找到索引 ${updateData.index} 对应的数据`)
    }

    if (!targetItem.hasOwnProperty('text')) {
      throw new Error(`索引 ${updateData.index} 的数据缺少 text 字段`)
    }

    // 更新段落文本
    targetItem.text = updateData.newText

    // 同步更新 wordDetails 中的词汇文本，保持 id 不变
    if (targetItem.wordDetails && Array.isArray(targetItem.wordDetails)) {
      console.log('原始 wordDetails:', targetItem.wordDetails)
      console.log('新文本:', updateData.newText)

      targetItem.wordDetails = distributeTextToWordDetails(updateData.newText, targetItem.wordDetails)

      console.log('更新后 wordDetails:', targetItem.wordDetails)
    }

    // 更新 record 中的 paragraphs 字段
    const updatedParagraphs = JSON.stringify(paragraphsData)
    record.value.paragraphs = updatedParagraphs

    // 保存到数据库
    await saveTextToDatabase(updatedParagraphs)

    // 如果有回调函数，调用成功回调
    if (updateData.resolve) {
      updateData.resolve()
    }
  } catch (error) {
    console.error('更新文本失败:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      updateData: updateData
    })

    // 如果有回调函数，调用失败回调
    if (updateData.reject) {
      updateData.reject(error)
    } else {
      ElMessage.error('保存失败，请重试')
    }
  }
}

// 保存文本更改到数据库
const saveTextToDatabase = async (updatedParagraphs) => {
  try {
    const updateData = {
      vaId: recordId,
      paragraphs: updatedParagraphs
    }

    const response = await updateTingwuTrans(updateData)

    if (response.code === 200) {
      console.log('数据库更新成功')
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存到数据库失败:', error)
    throw error
  }
}

// 处理关键词更新
const handleKeywordsUpdate = async (updateData) => {
  try {
    const { recordId: id, tags, originalTags } = updateData

    console.log('更新关键词:', { id, tags, originalTags })

    // 准备更新数据
    const requestData = {
      vaId: id,
      tags: tags
    }

    // 调用API更新数据库
    const response = await updateTingwuTrans(requestData)

    if (response.code === 200) {
      // 更新本地数据
      record.value.tags = tags
      console.log('关键词更新成功')
    } else {
      throw new Error(response.msg || '保存关键词失败')
    }
  } catch (error) {
    console.error('保存关键词失败:', error)
    ElMessage.error('保存关键词失败，请重试')
    throw error
  }
}

// 处理关键词点击事件
const handleKeywordClick = (keyword) => {
  // 显示搜索框
  showSearchBox.value = true

  // 设置搜索关键词
  searchKeyword.value = keyword

  // 执行搜索
  performSearch()
}




onMounted(async() => {
  //根据id查询具体的数据
  const res = await getTingwuTrans(recordId)
  record.value = res.data
  console.log('res.data>>>>', res.data)
  const path = res.data.vaPath
  const url = await getUrlByAudioPath(path)
  console.log('url>>>>', url)
  audioUrl.value = url.data
  console.log('音频URL:', audioUrl.value)

})
</script>


<template>
  <div class="record-detail">
    <div class="record-container" v-if="record">
      <!-- 头部标题 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">{{ record.vaName }}</h1>
        </div>
        <div class="header-right">
          <!-- 导出功能 -->
          <ExportDialog
            :record="record"
            :conversations="formattedConversations"
          />

          <!-- 搜索功能 -->
          <div class="search-container">
            <!-- 搜索按钮 -->
            <el-button
              v-if="!showSearchBox"
              @click="toggleSearch"
              type="primary"
              :icon="Search"
              circle
              size="default"
              class="search-toggle-btn"
              title="搜索原文"
            />

            <!-- 搜索框区域 -->
            <div v-if="showSearchBox" class="search-box-container">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索原文内容..."
                size="default"
                class="search-input"
                clearable
                @input="performSearch"
                @keyup.enter="performSearch"
                @clear="clearSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>

              <!-- 搜索结果导航 -->
              <div v-if="searchResults.length > 0" class="search-navigation">
                <span class="search-count">
                  {{ currentSearchIndex + 1 }}/{{ searchResults.length }}
                </span>
                <el-button-group class="search-nav-buttons">
                  <el-button
                    @click="prevSearchResult"
                    :icon="ArrowUp"
                    size="small"
                    :disabled="searchResults.length === 0"
                  />
                  <el-button
                    @click="nextSearchResult"
                    :icon="ArrowDown"
                    size="small"
                    :disabled="searchResults.length === 0"
                  />
                </el-button-group>
              </div>

              <!-- 关闭搜索按钮 -->
              <el-button
                @click="toggleSearch"
                :icon="Close"
                size="small"
                circle
                class="search-close-btn"
                title="关闭搜索"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧原文区域 -->
        <div class="left-panel">
          <!-- 关键词标签 -->
          <KeywordsSection
            :tags="record.tags"
            :record-id="recordId"
            @update:tags="handleKeywordsUpdate"
            @keyword-click="handleKeywordClick"
          />

          <!-- 对话列表 -->
          <ConversationList
            :paragraphs="record.paragraphs"
            :current-playing-index="currentPlayingIndex"
            :current-playing-time="currentPlayingTime"
            :search-keyword="searchKeyword"
            :search-results="searchResults"
            :current-search-index="currentSearchIndex"
            @conversation-click="handleConversationClick"
            @text-updated="handleTextUpdate"
          />
        </div>

        <!-- 右侧总结区域 -->
        <RightPanel :record="record" />
      </div>

      <AudioPlayer
        ref="audioPlayerRef"
        :url="audioUrl"
        :conversations="formattedConversations"
        @timeUpdate="handleAudioTimeUpdate"
      />
    </div>

    <div v-else class="empty-wrap">
      <el-empty description="暂无详情" />
    </div>
  </div>
</template>

<style scoped>
.record-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0;
}

.record-container {
  max-width: 100%;
  margin: 0;
}

.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 20px 32px;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  text-align: left;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 搜索功能样式 */
.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-toggle-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.search-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.search-box-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(8px);
  animation: searchBoxSlideIn 0.3s ease-out;
}

@keyframes searchBoxSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-input {
  width: 280px;
}

.search-input :deep(.el-input__wrapper) {
  border: none;
  box-shadow: none;
  background: transparent;
}

.search-input :deep(.el-input__inner) {
  font-size: 14px;
  color: #374151;
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-left: 1px solid #e5e7eb;
}

.search-count {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.search-nav-buttons {
  display: flex;
}

.search-nav-buttons .el-button {
  padding: 4px 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #6b7280;
  transition: all 0.2s ease;
}

.search-nav-buttons .el-button:hover:not(:disabled) {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.search-nav-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-close-btn {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  transition: all 0.2s ease;
}

.search-close-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
  color: #dc2626;
}

.main-content {
  display: flex;
  background: #ffffff;
  min-height: calc(100vh - 120px);
  gap: 2px;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

.left-panel {
  flex: 0 0 60%;
  max-width: 60%;
  padding: 32px 32px 140px 32px; /* 增加底部内边距为音频播放器预留空间 */
  border-right: 2px solid #f1f5f9;
  overflow-y: auto;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
}

.empty-wrap {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 60px 0;
  max-width: 800px;
  margin: 0 auto;
}

</style>