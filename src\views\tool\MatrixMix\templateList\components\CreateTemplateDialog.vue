<template>
    <el-dialog
      v-model="visibleProxy"
      title="创建新模板"
      width="600px"
      :close-on-click-modal="false"
      class="factory-dialog"
      @close="handleClose"
    >
      <el-form :model="form" label-width="100px" v-loading="formLoading" class="dialog-form">
        <el-form-item label="模板名称" required>
          <el-input v-model="form.Name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="form.Type" disabled>
            <el-option label="普通模板" value="Timeline"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容 (Config)" prop="Config">
          <ConfigBuilder v-model:config="form.Config" style="width: 470px;"/>
        </el-form-item>
        <el-form-item label="封面URL">
          <el-upload
            class="cover-uploader"
            style="width: 470px; height: 148px;"
            action="#"
            :http-request="handleCoverUpload"
            :show-file-list="false"
            :before-upload="beforeCoverUpload"
            list-type="picture-card"
          >
            <img v-if="form.CoverUrl" :src="form.CoverUrl" class="cover-image" style="width: 100%; height: 100%; object-fit: cover;"/>
            <el-icon v-else class="el-icon-plus cover-uploader-icon"></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmCreate" :loading="formLoading">确认创建</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, watch, defineEmits, defineProps } from 'vue';
  import { ElMessage } from 'element-plus';
  import type { UploadRawFile, UploadRequestOptions } from 'element-plus';
  import { addTemplate } from '../../api/template';
  import { uploadFileUnified } from '@/api/file/info.js';
  import type { AddTemplateRequest } from '../../types/template';
  import ConfigBuilder from './ConfigBuilder.vue';
  
  const props = defineProps<{ visible: boolean }>();
  const emit = defineEmits(['update:visible', 'success']);
  
  const visibleProxy = ref(props.visible);
  watch(() => props.visible, v => (visibleProxy.value = v));
  watch(visibleProxy, v => emit('update:visible', v));
  
  const form = ref<AddTemplateRequest>({
    Type: 'Timeline',
    Name: '',
    Config: '',
    CoverUrl: ''
  });
  const formLoading = ref(false);
  
  const closeDialog = () => {
    visibleProxy.value = false;
  };
  const handleClose = () => {
    // 重置表单
    form.value = { Type: 'Timeline', Name: '', Config: '', CoverUrl: '' };
  };
  
  const handleConfirmCreate = async () => {
    if (!form.value.Name || !form.value.Config) {
      ElMessage.warning('模板名称和Config为必填项');
      return;
    }
    formLoading.value = true;
    try {
      const res = await addTemplate(form.value);
      if (res.code === 200) {
        ElMessage.success('模板创建成功');
        closeDialog();
        emit('success');
      } else {
        ElMessage.error(`创建失败: ${res.msg}`);
      }
    } catch (error) {
      ElMessage.error('创建模板时发生网络错误');
    } finally {
      formLoading.value = false;
    }
  };
  
  const beforeCoverUpload = (rawFile: UploadRawFile) => {
    const isImage = rawFile.type.startsWith('image/');
    if (!isImage) {
      ElMessage.error('请上传图片格式文件!');
      return false;
    }
    const isLt512K = rawFile.size / 1024 < 512;
    if (!isLt512K) {
      ElMessage.error('上传封面图片大小不能超过 512KB!');
      return false;
    }
    return true;
  };
  
  const handleCoverUpload = async (options: UploadRequestOptions) => {
    const { file } = options;
    try {
      const storageType = 'oss';
      const clientName = 'default';
      const res = await uploadFileUnified({ storageType, clientName, file });
      if (res.code === 200 && res.data && res.data.url) {
        form.value.CoverUrl = res.data.url;
        ElMessage.success('封面上传成功');
      } else {
        ElMessage.error(res.msg || '封面上传失败');
      }
    } catch (error) {
      ElMessage.error('封面上传时发生网络错误');
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .factory-dialog {
    .el-dialog__header {
      font-size: 20px;
      font-weight: 700;
      color: #222;
      padding-bottom: 0;
    }
    .el-dialog__body {
      padding: 24px 32px 8px 32px;
      background: #f7f8fa;
      border-radius: 0 0 16px 16px;
    }
    .dialog-form {
      .el-form-item {
        margin-bottom: 18px;
      }
      .el-input, .el-select {
        border-radius: 8px;
      }
    }
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 10px 32px 18px 32px;
    }
  }
  .cover-uploader .el-upload {
    border: 1.5px dashed #d9d9d9;
    border-radius: 10px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    background: #fafbfc;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .cover-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
    background: #f0f7ff;
  }
  .cover-uploader-icon {
    font-size: 32px;
    color: #8c939d;
    text-align: center;
  }
  .cover-image {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
    object-fit: cover;
  }
  </style>    