<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { CanvasRenderEngine } from '../../utils/canvasRenderEngine';
import type { Timeline } from '../../types/videoEdit';
import { formatRulerTime } from '@/views/tool/MatrixMix/utils/timeUtils';
import { createPlaybackValidator, type PlaybackValidationResult } from '../../utils/timelinePlaybackUtils';
import { 
  createCanvasResizeManager, 
  CanvasEngineManager, 
  createCanvasDebugManager 
} from '../../utils/canvasManagerUtils';


const props = defineProps<{
  timeline: Timeline | null;
  source?: string | null;
}>();

const emit = defineEmits<{
  (e: 'update:isPlaying', value: boolean): void;
  (e: 'update:currentTime', value: number): void;
  (e: 'update:duration', value: { formatted: string, seconds: number }): void;
  (e: 'active-video-element-changed', value: HTMLVideoElement | null): void;
  (e: 'engine-initialized', value: { engine: any, playerInstance: any }): void;
}>();

const canvasRef = ref<HTMLCanvasElement | null>(null);
const singlePlayerContainer = ref<HTMLDivElement | null>(null);
const isLoading = ref(false);
const errorMessage = ref<string>('');
const loadingStatus = ref<string>('');

const canvasWidth = ref(1280);
const canvasHeight = ref(720);

let renderEngine: CanvasRenderEngine | null = null;
let singlePlayer: any = null;

// 播放校验器
let playbackValidator = createPlaybackValidator(null);

// Canvas 管理器
const canvasResizeManager = createCanvasResizeManager(canvasRef, {
  defaultWidth: 1280,
  defaultHeight: 720,
  autoResize: true
});

const canvasEngineManager = new CanvasEngineManager();
const canvasDebugManager = createCanvasDebugManager(canvasRef, import.meta.env.DEV);

// 设置 Canvas 管理器回调
canvasResizeManager.setResizeCallback((size) => {
  canvasWidth.value = size.width;
  canvasHeight.value = size.height;
  canvasDebugManager.updateCanvasSize(size.width, size.height);
  
  if (renderEngine && typeof renderEngine.resize === 'function') {
    renderEngine.resize(size.width, size.height);
  }
});

canvasEngineManager.setCallbacks({
  onLoadingChange: (loading, status) => {
    // 只有在真正的加载状态变化时才更新UI状态
    if (loading) {
      isLoading.value = true;
      loadingStatus.value = status || '初始化中...';
      canvasDebugManager.updateEngineStatus('初始化中...');
    } else {
      // 引擎初始化完成，但保持加载状态，等待媒体资源加载完成
      canvasDebugManager.updateEngineStatus('已初始化');
      if (status && !status.includes('初始化完成')) {
        loadingStatus.value = status;
      }
    }
  },
  onError: (error) => {
    errorMessage.value = error;
    isLoading.value = false;
    loadingStatus.value = '';
  },
  onInitialized: (engine) => {
    renderEngine = engine;
    canvasDebugManager.updateEngineStatus('已初始化');
    // **新增**: 当渲染引擎重新初始化时，通知父组件重新设置音频电平监控
    emit('engine-initialized', {
      engine,
      playerInstance: {
        play,
        pause,
        seek,
        getRenderEngine: () => renderEngine
      }
    });
  }
});

const videoClipCount = computed(() => {
  return props.timeline?.VideoTracks?.[0]?.VideoTrackClips?.length || 0;
});

const audioClipCount = computed(() => {
  return props.timeline?.AudioTracks?.[0]?.AudioTrackClips?.length || 0;
});

// 使用调试管理器提供的显示状态
const showDebugInfo = computed(() => canvasDebugManager.shouldShowDebug());

// 获取调试信息
const debugInfo = computed(() => {
  const info = canvasDebugManager.getDebugInfo();
  // 实时更新片段数量
  canvasDebugManager.updateClipCounts(videoClipCount.value, audioClipCount.value);
  // 实时更新播放信息
  canvasDebugManager.updatePlaybackInfo(currentPlayTime.value, maxTrackDuration.value);
  return info;
});

// 计算最长轨道的持续时间（使用工具类）
const maxTrackDuration = computed(() => {
  return playbackValidator.getMaxDuration();
});

// 当前播放时间，用于播放校验
const currentPlayTime = ref(0);

// 音频混合控制
const videoAudioVolume = ref(1.0);
const timelineAudioVolume = ref(1.0);
const showAudioControls = ref(false);

watch(
  () => ({ timeline: props.timeline }),
  async ({ timeline }) => {
    try {
      errorMessage.value = '';
      
      // 更新播放校验器的时间轴数据
      playbackValidator.updateTimeline(timeline);
      
      // 更新调试管理器的时间线状态
      canvasDebugManager.updateTimelineStatus(timeline ? '已加载' : '未加载');
      
      if (timeline) {
        await initializeCanvasEngine(timeline);
      } else {
        cleanup();
      }
    } catch (error) {
      console.error('初始化失败:', error);
      errorMessage.value = `初始化失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  canvasResizeManager.updateCanvasSize();
  canvasResizeManager.startAutoResize();
});

onUnmounted(() => {
  cleanup();
  canvasResizeManager.destroy();
  canvasEngineManager.destroy();
});

function updateCanvasSize() {
  canvasResizeManager.updateCanvasSize();
}

async function initializeCanvasEngine(timeline: Timeline) {
  cleanup();
  
  if (!canvasRef.value) return;
  
  try {
    // 使用 Canvas 引擎管理器初始化渲染引擎
    await canvasEngineManager.initializeEngine(CanvasRenderEngine, canvasRef.value);
    
    // 获取初始化后的引擎实例
    renderEngine = canvasEngineManager.getEngine();
    
    if (renderEngine) {
      renderEngine.setEventCallbacks({
        onTimeUpdate: (time) => {
          currentPlayTime.value = time;
          emit('update:currentTime', time);
          
          // 播放校验：使用工具类进行时间校验
          const validation = playbackValidator.validateTime(time);
          if (validation.shouldPause) {
            console.log('⏹️', validation.message);
            pause();
          }
        },
        onPlayStateChange: (isPlaying) => {
          emit('update:isPlaying', isPlaying);
        },
        onDurationChange: (duration) => {
          emit('update:duration', {
            formatted: formatRulerTime(duration),
            seconds: duration
          });
        }
      });
      
      loadingStatus.value = '加载媒体资源...';
      await renderEngine.setTimeline(timeline);
      loadingStatus.value = '媒体资源加载完成';

      // 设置初始音量
      renderEngine.setVideoAudioVolume(videoAudioVolume.value);
      renderEngine.setTimelineAudioVolume(timelineAudioVolume.value);

      await nextTick();
      if (renderEngine) {
        // 立即渲染第一帧
        renderEngine.seek(0);
        
        // 延迟一点时间确保渲染完成后再隐藏加载遮罩，但不再重复调用seek
        setTimeout(() => {
          if (renderEngine) {
            // 媒体资源和首帧渲染完成，清除加载状态
            isLoading.value = false;
            loadingStatus.value = '';
          }
        }, 100);
      }
    }
    
  } catch (error) {
    console.error('Canvas 渲染引擎初始化失败:', error);
    errorMessage.value = `渲染引擎初始化失败: ${error instanceof Error ? error.message : '未知错误'}`;
  }
}


function cleanup() {
  // 使用 Canvas 引擎管理器清理
  canvasEngineManager.cleanup();
  renderEngine = null;
  
  if (singlePlayer) {
    singlePlayer.dispose();
    singlePlayer = null;
  }
  
  if (singlePlayerContainer.value) {
    singlePlayerContainer.value.innerHTML = '';
  }
  
  emit('active-video-element-changed', null);
}

function play() {
  // 播放前校验：使用工具类进行开始播放校验
  const validation = playbackValidator.validateStart(currentPlayTime.value);
  if (validation.shouldResetToStart) {
    console.log('🔄', validation.message);
    seek(0);
  }
  
  if (renderEngine) {
    renderEngine.play();
  } else if (singlePlayer) {
    singlePlayer.play();
  }
}

function pause() {
  if (renderEngine) {
    renderEngine.pause();
  } else if (singlePlayer) {
    singlePlayer.pause();
  }
}

function seek(time: number) {
  // 播放位置校验：使用工具类进行跳转位置校验
  const validation = playbackValidator.validateSeek(time);
  const seekTime = validation.actualSeekTime || time;
  
  if (!validation.isValid) {
    console.log('⚠️', validation.message);
  }
  
  currentPlayTime.value = seekTime;
  
  if (renderEngine) {
    renderEngine.seek(seekTime);
  } else if (singlePlayer) {
    singlePlayer.seek(seekTime);
    singlePlayer.pause();
  }
}


function retryInitialize() {
  errorMessage.value = '';
  if (props.timeline) {
    initializeCanvasEngine(props.timeline);
  } else{
    console.error('props.timeline有问题 fuck aliyun',props.timeline);
  }
}

// 音频混合控制方法
function setVideoVolume(volume: number) {
  videoAudioVolume.value = volume;
  if (renderEngine) {
    renderEngine.setVideoAudioVolume(volume);
  }
}

function setTimelineVolume(volume: number) {
  timelineAudioVolume.value = volume;
  if (renderEngine) {
    renderEngine.setTimelineAudioVolume(volume);
  }
}

function toggleAudioControls() {
  showAudioControls.value = !showAudioControls.value;
}

defineExpose({
  play,
  pause,
  seek,
  // **新增**: 暴露渲染引擎访问方法
  getRenderEngine: () => renderEngine
});
</script>

<template>
  <div class="timeline-player-wrapper">
    <!-- Canvas 渲染容器 -->
    <canvas 
      ref="canvasRef" 
      class="render-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
    />
    <!-- 调试信息显示 -->
    <div v-if="showDebugInfo && renderEngine" class="debug-overlay">
      <div>Canvas: {{ debugInfo.canvasSize.width }}x{{ debugInfo.canvasSize.height }}</div>
      <div>渲染引擎: {{ debugInfo.engineStatus }}</div>
      <div>时间线: {{ debugInfo.timelineStatus }}</div>
      <div v-if="timeline">片段数量: V{{ debugInfo.clipCounts.video }} A{{ debugInfo.clipCounts.audio }}</div>
      <div v-if="maxTrackDuration > 0">最长轨道: {{ formatRulerTime(debugInfo.playbackInfo.maxDuration) }}s</div>
      <div>当前时间: {{ formatRulerTime(debugInfo.playbackInfo.currentTime) }}s</div>
    </div>
    
    <!-- 音频混合控制面板 -->
    <div class="audio-controls-panel" :class="{ 'show': showAudioControls }">
      <div class="audio-controls-header">
        <span>🎵 音频混合</span>
        <button @click="toggleAudioControls" class="close-btn">×</button>
      </div>
      <div class="audio-controls-content">
        <div class="volume-control">
          <label>视频音频:</label>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1"
            :value="videoAudioVolume"
            @input="setVideoVolume(parseFloat(($event.target as HTMLInputElement).value))"
          />
          <span>{{ Math.round(videoAudioVolume * 100) }}%</span>
        </div>
        <div class="volume-control">
          <label>时间轴音频:</label>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1"
            :value="timelineAudioVolume"
            @input="setTimelineVolume(parseFloat(($event.target as HTMLInputElement).value))"
          />
          <span>{{ Math.round(timelineAudioVolume * 100) }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 音频控制按钮 -->
    <button v-if="!showAudioControls" @click="toggleAudioControls" class="audio-control-btn">
      🎵
    </button>
    
    <!-- 加载遮罩层 -->
    <div v-if="isLoading" class="loading-overlay">
      <p>正在初始化渲染引擎...</p>
      <div v-if="loadingStatus" class="loading-status">{{ loadingStatus }}</div>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-overlay">
      <p>{{ errorMessage }}</p>
      <button @click="retryInitialize" class="retry-button">重试</button>
    </div>
  </div>
</template>



<style scoped>
.timeline-player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
}

.render-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.single-player-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.debug-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  z-index: 10;
  
  div {
    margin-bottom: 4px;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  z-index: 20;
  
  .loading-status {
    margin-top: 10px;
    font-size: 0.9em;
    color: #ccc;
  }
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(139, 0, 0, 0.9);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.1em;
  z-index: 30;
  
  .retry-button {
    margin-top: 15px;
    padding: 8px 16px;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #005a9e;
    }
  }
}

/* 音频控制面板 */
.audio-controls-panel {
  position: absolute;
  top: 50px;
  right: 10px;
  width: 250px;
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 16px;
  color: white;
  z-index: 40;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  
  &.show {
    transform: translateX(0);
  }
  
  .audio-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: bold;
    
    .close-btn {
      background: none;
      border: none;
      color: #ccc;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      
      &:hover {
        color: white;
      }
    }
  }
  
  .volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    label {
      min-width: 80px;
      font-size: 12px;
      color: #ccc;
    }
    
    input[type="range"] {
      flex: 1;
      height: 4px;
      background: #444;
      border-radius: 2px;
      outline: none;
      
      &::-webkit-slider-thumb {
        appearance: none;
        width: 16px;
        height: 16px;
        background: #007acc;
        border-radius: 50%;
        cursor: pointer;
      }
      
      &::-moz-range-thumb {
        width: 16px;
        height: 16px;
        background: #007acc;
        border-radius: 50%;
        cursor: pointer;
        border: none;
      }
    }
    
    span {
      min-width: 35px;
      font-size: 12px;
      color: #ccc;
      text-align: right;
    }
  }
}

/* 音频控制按钮 */
.audio-control-btn {
  position: absolute;
  top: 50px;
  right: 10px;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  cursor: pointer;
  z-index: 35;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
}
</style>