# 调试系统使用说明

## 概述
`templateDataDebug.ts` 提供了一个集中的调试系统，支持分类别的日志控制和输出。

## 主要功能

### 1. 配置开关
- `enableCanvasRenderLogging`: Canvas渲染日志开关
- `enableTimelineDataLogging`: Timeline数据日志开关  
- `enableStoreStateLogging`: Store状态日志开关
- `enableLogCaching`: 日志缓存开关

### 2. 调试方法

#### Canvas相关
```typescript
// Canvas渲染日志
debugTool.logCanvasRender('开始渲染帧', frameData);

// Canvas音频级别
debugTool.logCanvasAudioLevels(audioLevels, extraInfo);

// Canvas性能监控
debugTool.logCanvasPerformance('轨道渲染', 25.6);

// Canvas轨道渲染
debugTool.logCanvasTrackRender('Video', 0, 3);
```

#### Timeline相关
```typescript
// Timeline数据结构检查
debugTool.debugTimelineData(timelineData);

// 计算属性轨道检查
debugTool.debugComputedTracks(videoTracks, 'Video');
```

#### Store相关
```typescript
// Store状态检查
debugTool.debugStoreState(store);
```

### 3. 开关控制
```typescript
// 动态开关控制
debugTool.toggleCanvasLogging(true);   // 启用Canvas日志
debugTool.toggleTimelineLogging(false); // 禁用Timeline日志
debugTool.toggleStoreLogging(true);    // 启用Store日志
debugTool.toggleLogCaching(false);     // 禁用日志缓存

// 获取当前状态
const status = debugTool.getDebugStatus();
console.log(status);
```

### 4. 强制日志
```typescript
// 重要信息，忽略开关设置
debugTool.forceLog('error', '严重错误', errorData);
```

### 5. 工具方法
```typescript
// 内存使用分析
debugTool.analyzeMemoryUsage();

// 导出日志
debugTool.exportLogs();

// 清空缓存
debugTool.clearLogs();
```

## 使用原则

1. **开关控制**: 所有调试方法都会检查对应的开关，`false`时不生成日志也不存入缓存
2. **分类管理**: 不同类型的调试信息使用对应的开关控制
3. **性能友好**: 开关关闭时，调试代码几乎不消耗性能
4. **强制输出**: 使用`forceLog`处理重要错误或警告信息

## 初始化
```typescript
import { TemplateDataDebug } from './debug/templateDataDebug';

const debugTool = new TemplateDataDebug();

// 默认所有开关都是关闭的，需要手动开启需要的调试类别
debugTool.toggleCanvasLogging(true);
```
