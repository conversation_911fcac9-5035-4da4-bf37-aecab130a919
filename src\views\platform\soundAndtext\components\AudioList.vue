<template>
  <div class="div-container">
    <div class="audio-list-toolbar">
      <div class="audio-list-title">
          <el-icon><Microphone /></el-icon>
          <span>音频列表</span>
        </div>
      <div class="audio-list-toolbar-right">
        <el-checkbox v-model="allSelected" @change="toggleSelectAll">全选</el-checkbox>
        <el-button type="danger" size="small" :disabled="selectedIds.length === 0" @click="handleDelete()">批量删除</el-button>
        <el-button type="info" size="small" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="audio-grid">
      <div v-for="item in audioList" :key="item.audioId" class="audio-card">
        <div class="card-header">
          <div class="title-section">
            <el-checkbox v-model="selectedIds" :label="item.audioId" class="audio-checkbox" />
            <el-tooltip :content="item.content" placement="top" effect="dark">
              <span class="title-text" @click="handleGoToSynthesis(item)">{{ item.audioName }}</span>
            </el-tooltip>
            <el-button type="danger" size="small" @click.stop="handleDelete(item.audioId)">删除</el-button>
          </div>
        </div>
        <div class="player-section">
          <audioPlayer :audioSrc="item.audioId" style="width: 100%;"/>
        </div>
      </div>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getAudioList"
      class="pagination-container"
    />
    <SelectSynthesisVersion
      v-model:visible="showVersionDialog"
      :audioInfo="selectedAudio"
      @version-selected="onVersionSelected"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import audioPlayer from '@/views/platform/material/components/audio-player'
import { audioAndText, delAudio } from '@/api/platform/audio'
import Pagination from '@/components/Pagination/index.vue'
import SelectSynthesisVersion from './SelectSynthesisVersion.vue'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  versionKey: String
})

const audioList = ref([])
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 9
})

const showVersionDialog = ref(false)
const selectedAudio = ref(null)
const selectedIds = ref([])
const allSelected = ref(false)

const router = useRouter()
const route = useRoute()

// 删除localStorage相关逻辑，点击音频都弹窗
const handleGoToSynthesis = (audio) => {
  const audioPath = audio.audioPath || audio.audioAddress || audio.path || audio.address || audio.filePath
  if (!audioPath) {
    ElMessage.error('音频路径无效，无法选择');
    return;
  }
  selectedAudio.value = { audioPath, audioName: audio.audioName }
  if (props.versionKey) {
    // 有版本参数，直接跳转
    let versionRoute = ''
    if (props.versionKey === 'm') versionRoute = '/szbVideo/createMusetalk'
    else if (props.versionKey === 'v') versionRoute = '/szbVideo/synthesisCreate'
    else if (props.versionKey === 'h') versionRoute = '/szbVideo/createHeygem'
    if (versionRoute) {
      const query = {
        audioPath,
        audioName: audio.audioName,
        from: 'textAudio',
      }
      if (route.query.keepImageAddress) query.keepImageAddress = route.query.keepImageAddress
      if (route.query.imageAddress) query.imageAddress = route.query.imageAddress
      router.push({ path: versionRoute, query })
      return
    }
  }
  // 没有版本参数，弹窗
  showVersionDialog.value = true
}

// 版本选择弹窗回调，选择后直接跳转
function onVersionSelected(versionKey) {
  if (versionKey) {
    const audio = selectedAudio.value
    if (audio) {
      let versionRoute = ''
      if (versionKey === 'm') versionRoute = '/szbVideo/createMusetalk'
      else if (versionKey === 'v') versionRoute = '/szbVideo/synthesisCreate'
      else if (versionKey === 'h') versionRoute = '/szbVideo/createHeygem'
      if (versionRoute) {
        const query = {
          audioPath: audio.audioPath,
          audioName: audio.audioName,
          from: 'textAudio',
        }
        if (route.query.keepImageAddress) query.keepImageAddress = route.query.keepImageAddress
        if (route.query.imageAddress) query.imageAddress = route.query.imageAddress
        router.push({ path: versionRoute, query })
      }
    }
  }
}

const getAudioList = async () => {
  try {
    const response = await audioAndText(queryParams.value)
    audioList.value = (response.rows || []).sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    total.value = response.total || 0
    selectedIds.value = []
    allSelected.value = false
  } catch (error) {
    console.error('获取音频列表失败:', error)
  }
}

// 单个或批量删除
function handleDelete(id) {
  let ids = []
  if (id) {
    ids = [id]
  } else {
    ids = selectedIds.value
  }
  if (!ids.length) return
  ElMessageBox.confirm(
    `是否确认删除选中的${ids.length}个音频？`,
    '删除',
    { type: 'warning', confirmButtonText: '删除', cancelButtonText: '取消' }
  ).then(async () => {
    await delAudio(ids)
    ElMessage.success('删除成功')
    getAudioList()
  }).catch(() => {})
}

// 重置操作，清空选中项、重置分页并刷新列表
function handleReset() {
  selectedIds.value = [];
  allSelected.value = false;
  queryParams.value.pageNum = 1;
  queryParams.value.pageSize = 9;
  getAudioList();
}

function toggleSelectAll(val) {
  if (val) {
    selectedIds.value = audioList.value.map(item => item.audioId)
  } else {
    selectedIds.value = []
  }
}

onMounted(() => {
  getAudioList()
})
</script>

<style lang="scss" scoped>
.div-container {
  width: 100%;
  padding: 20px;
}
.audio-list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.pagination-container{
  background-color: #ffffff;
}
.audio-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #3b3f5c;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 1px;
}
.audio-list-toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}
.audio-checkbox {
  margin-right: 8px;
}
.audio-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 0 auto;
  box-sizing: border-box;
}
.audio-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e8eaed;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.08);
    border-color: #667eea;
  }
  .card-header {
    padding: 16px 16px 0 16px;
    background: linear-gradient(to right, #f8fafc, white);
    border-bottom: 1px solid #e2e8f0;
  }
  .title-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #409EFF;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 70%;
      padding: 4px 8px;
      border-radius: 4px;
      &:hover {
        color: #66b1ff !important;
        text-decoration: underline !important;
        background: rgba(64, 158, 255, 0.1);
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
      &:active {
        transform: scale(0.98);
      }
    }
  }
  .player-section {
    padding: 16px;
    background: linear-gradient(to bottom, white, #f8fafc);
  }
}
@media (max-width: 1400px) {
  .audio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 900px) {
  .audio-grid {
    grid-template-columns: 1fr;
  }
}
</style>