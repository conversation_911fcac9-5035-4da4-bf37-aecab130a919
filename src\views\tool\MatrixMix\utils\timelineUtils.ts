/**
 * 时间轴工具函数 - 精简版
 * 只保留实际使用的函数
 */

import type { Timeline, TimelineClip } from '../types/videoEdit';

export type TrackType = 'video' | 'audio' | 'subtitle';

/**
 * @interface TimelineOperationResult
 * @description 时间轴操作的统一返回结果格式
 */
export interface TimelineOperationResult {
  success: boolean;
  timeline?: Timeline;
  error?: string;
}

/**
 * @interface TrackClipsResult
 * @description 获取轨道片段的结果接口
 */
export interface TrackClipsResult {
  trackClips: TimelineClip[] | undefined;
  found: boolean;
}

export interface DragCalculationResult {
  targetTrackIndex: number | null;
  isValidDrop: boolean;
  isNewTrack: boolean;
  insertPosition?: 'above' | 'below' | 'replace';
  snapZone?: 'top' | 'center' | 'bottom';
}

export interface ProcessedTrack {
  originalIndex: number;
  clips: any[];
  uiIndex?: number;
  isHighlighted?: boolean;
  isPreviewTrack?: boolean;
}

/**
 * 处理轨道数据，将原始轨道数据转换为显示用的数据结构
 */
export function processTrackData(tracks: any[], clipMapper: (clip: any) => any): ProcessedTrack[] {
  return tracks.map((track, index) => ({
    originalIndex: index,
    clips: track.clips ? track.clips.map(clipMapper) : [],
    uiIndex: index
  }));
}

/**
 * 创建预览轨道
 */
function createPreviewTrack(trackType: TrackType, targetIndex: number): ProcessedTrack {
  return {
    originalIndex: -1,
    clips: [],
    uiIndex: -1,
    isPreviewTrack: true
  };
}

/**
 * 向显示轨道数组中添加预览轨道，支持精确的轨道插入和吸附
 */
export function addPreviewTrackToDisplayTracks(
  tracks: ProcessedTrack[],
  trackType: TrackType,
  dragResult: DragCalculationResult,
  isShowingPreview: boolean
): ProcessedTrack[] {
  if (!isShowingPreview || !dragResult.isValidDrop || !dragResult.isNewTrack) {
    return tracks.map((track, index) => ({
      ...track,
      uiIndex: index,
      isHighlighted: !dragResult.isNewTrack && dragResult.targetTrackIndex === track.originalIndex
    }));
  }

  const { targetTrackIndex, insertPosition } = dragResult;
  const result = [...tracks];
  
  // 创建预览轨道
  const previewTrack = {
    ...createPreviewTrack(trackType, targetTrackIndex!),
    insertPosition,
    isHighlighted: true
  };

  // 根据插入位置添加预览轨道
  if (insertPosition === 'above') {
    // 找到对应的UI索引位置进行插入
    const uiInsertIndex = result.findIndex(track => 
      track.originalIndex >= targetTrackIndex!
    );
    
    if (uiInsertIndex >= 0) {
      result.splice(uiInsertIndex, 0, previewTrack);
    } else {
      result.unshift(previewTrack); // 插入到最前面
    }
  } else if (insertPosition === 'below') {
    // 找到对应的UI索引位置进行插入  
    const uiInsertIndex = result.findIndex(track => 
      track.originalIndex >= targetTrackIndex!
    );
    
    if (uiInsertIndex >= 0) {
      result.splice(uiInsertIndex, 0, previewTrack);
    } else {
      result.push(previewTrack); // 插入到最后面
    }
  } else {
    // 默认插入到末尾
    result.push(previewTrack);
  }

  // 重新分配UI索引
  result.forEach((track, index) => {
    track.uiIndex = index;
  });
  
  return result;
}

/**
 * 智能轨道拖拽计算，参考专业剪辑软件的交互逻辑
 */
export function smartTrackDragCalculation(params: {
  mouseY: number;
  trackType: TrackType;
  scrollAreaRect: DOMRect;
  scrollTop: number;
  trackHeight: number;
  displayTracks: any[];
  allTracks: any[];
  sourceTrackIndex?: number;
  dragStartY?: number;
  isDraggingFromLibrary?: boolean;
}): DragCalculationResult {
  const {
    mouseY,
    trackType,
    scrollAreaRect,
    scrollTop,
    trackHeight,
    displayTracks,
    allTracks,
    sourceTrackIndex,
    dragStartY,
    isDraggingFromLibrary = false
  } = params;

  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 如果没有任何轨道，创建第一个轨道
  if (displayTracks.length === 0) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'center'
    };
  }

  // 计算所有轨道的边界区域
  const trackRegions = displayTracks.map((track, uiIndex) => {
    const top = uiIndex * trackHeight;
    const bottom = top + trackHeight;
    const center = top + trackHeight / 2;
    
    return {
      uiIndex,
      track,
      top,
      center, 
      bottom,
      originalIndex: track.originalIndex
    };
  });

  // 添加拖拽区域边界（用于创建新轨道）
  const topBoundary = trackRegions[0]?.top || 0;
  const bottomBoundary = trackRegions[trackRegions.length - 1]?.bottom || trackHeight;
  
  // 定义吸附阈值
  const snapThreshold = trackHeight * 0.25;
  const insertThreshold = trackHeight * 0.3;
  
  // 拖拽到轨道区域上方 - 创建新轨道到顶部
  if (relativeY < topBoundary - snapThreshold) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  // 拖拽到轨道区域下方 - 创建新轨道到底部
  if (relativeY > bottomBoundary + snapThreshold) {
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'bottom'
    };
  }

  // 在轨道区域内 - 寻找最佳匹配
  let bestMatch: any = null;
  let minDistance = Infinity;
  let insertMode = false;

  for (const region of trackRegions) {
    // 计算到各个关键点的距离
    const distanceToTop = Math.abs(relativeY - region.top);
    const distanceToCenter = Math.abs(relativeY - region.center);
    const distanceToBottom = Math.abs(relativeY - region.bottom);
    
    const minRegionDistance = Math.min(distanceToTop, distanceToCenter, distanceToBottom);
    
    if (minRegionDistance < minDistance) {
      minDistance = minRegionDistance;
      bestMatch = region;
      
      // 判断是否进入插入模式
      insertMode = (distanceToTop < insertThreshold) || (distanceToBottom < insertThreshold);
    }
  }

  if (!bestMatch) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }

  // 计算拖拽意图
  const dragDirection = sourceTrackIndex !== undefined && dragStartY !== undefined ?
    (mouseY > dragStartY ? 'down' : 'up') : null;
  
  const positionInTrack = (relativeY - bestMatch.top) / trackHeight;
  
  // 智能判断最终操作
  let finalResult: DragCalculationResult;
  
  if (insertMode) {
    // 插入模式：创建新轨道
    const distanceToTop = Math.abs(relativeY - bestMatch.top);
    const distanceToBottom = Math.abs(relativeY - bestMatch.bottom);
    
    if (distanceToTop < distanceToBottom) {
      // 插入到上方
      finalResult = {
        targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex : 0,
        isValidDrop: true,
        isNewTrack: true,
        insertPosition: 'above',
        snapZone: 'top'
      };
    } else {
      // 插入到下方
      finalResult = {
        targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex + 1 : 1,
        isValidDrop: true,
        isNewTrack: true,
        insertPosition: 'below',
        snapZone: 'bottom'
      };
    }
  } else {
    // 替换模式：放到现有轨道
    finalResult = {
      targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex : 0,
      isValidDrop: true,
      isNewTrack: bestMatch.originalIndex === -1,
      insertPosition: 'replace',
      snapZone: 'center'
    };
  }

  // 从素材库拖拽时，优先创建新轨道
  if (isDraggingFromLibrary && finalResult.insertPosition === 'replace') {
    // 根据拖拽位置决定插入方向
    if (positionInTrack < 0.5) {
      finalResult.insertPosition = 'above';
      finalResult.isNewTrack = true;
    } else {
      finalResult.insertPosition = 'below'; 
      finalResult.isNewTrack = true;
      finalResult.targetTrackIndex = (finalResult.targetTrackIndex || 0) + 1;
    }
  }

  return finalResult;
}

/**
 * 根据鼠标位置精确计算目标轨道索引，支持轨道间插入和吸附，参考专业剪辑软件的拖拽行为
 */
export function calculateTargetTrackIndex(
  mouseY: number,
  trackType: TrackType,
  scrollAreaRect: DOMRect,
  scrollTop: number,
  trackHeight: number,
  displayTracks: any[],
  allTracks: any[],
  sourceTrackIndex?: number
): DragCalculationResult {
  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 参考阿里云智能媒体生产的轨道边界计算
  const trackBounds = displayTracks.map((track, index) => ({
    index,
    top: index * trackHeight,
    center: index * trackHeight + trackHeight / 2,
    bottom: (index + 1) * trackHeight,
    track
  }));

  // 动态阈值：根据轨道高度调整吸附区域，更精准的判断
  const insertThreshold = trackHeight * 0.25;  // 上下25%区域为插入区域
  const centerZone = trackHeight * 0.5;        // 中间50%为替换区域
  
  // 查找当前鼠标所在的轨道区域
  let currentTrackIndex = Math.floor(relativeY / trackHeight);
  currentTrackIndex = Math.max(0, Math.min(currentTrackIndex, displayTracks.length - 1));
  
  // 计算在当前轨道内的相对位置（0-1）
  const trackStartY = currentTrackIndex * trackHeight;
  const relativePositionInTrack = (relativeY - trackStartY) / trackHeight;
  
  // 根据相对位置和拖拽方向智能判断意图
  let insertPosition: 'above' | 'below' | 'replace' = 'replace';
  let targetTrackIndex = currentTrackIndex;
  let isNewTrack = false;
  
  // 处理拖拽到轨道区域外的情况（更严格的边界检查）
  if (relativeY < -insertThreshold) {
    // 拖拽到最上方，插入到第一个轨道之前
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  const totalTracksHeight = displayTracks.length * trackHeight;
  if (relativeY > totalTracksHeight + insertThreshold) {
    // 拖拽到最下方，添加新轨道
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below', 
      snapZone: 'bottom'
    };
  }
  
  // 智能判断拖拽意图（参考专业剪辑软件的行为）
  if (sourceTrackIndex !== undefined) {
    const isDraggingDown = sourceTrackIndex < currentTrackIndex;
    const isDraggingUp = sourceTrackIndex > currentTrackIndex;
    
    // 上边缘区域（0-25%）
    if (relativePositionInTrack < 0.25) {
      if (isDraggingDown) {
        // 从上往下拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else if (isDraggingUp && relativePositionInTrack < 0.15) {
        // 从下往上拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 下边缘区域（75%-100%）
    else if (relativePositionInTrack > 0.75) {
      if (isDraggingUp) {
        // 从下往上拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else if (isDraggingDown && relativePositionInTrack > 0.85) {
        // 从上往下拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 中心区域（25%-75%）
    else {
      // 中心区域优先考虑替换，除非拖拽距离很远
      const trackDistance = Math.abs(sourceTrackIndex - currentTrackIndex);
      if (trackDistance > 2) {
        // 长距离拖拽时，根据拖拽方向倾向于插入
        if (isDraggingDown && relativePositionInTrack > 0.6) {
          insertPosition = 'below';
          targetTrackIndex = currentTrackIndex + 1;
          isNewTrack = true;
        } else if (isDraggingUp && relativePositionInTrack < 0.4) {
          insertPosition = 'above';
          targetTrackIndex = currentTrackIndex;
          isNewTrack = true;
        } else {
          insertPosition = 'replace';
          targetTrackIndex = currentTrackIndex;
        }
      } else {
        // 短距离拖拽优先替换
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
  } else {
    // 没有源轨道信息时的简单判断
    if (relativePositionInTrack < 0.2) {
      insertPosition = 'above';
      targetTrackIndex = currentTrackIndex;
      isNewTrack = true;
    } else if (relativePositionInTrack > 0.8) {
      insertPosition = 'below';
      targetTrackIndex = currentTrackIndex + 1;
      isNewTrack = true;
    } else {
      insertPosition = 'replace';
      targetTrackIndex = currentTrackIndex;
    }
  }
  
  // 处理目标轨道不存在的情况
  const targetTrack = displayTracks[currentTrackIndex];
  if (!targetTrack) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }
  
  // 修正目标轨道索引（处理预览轨道）
  if (insertPosition === 'replace') {
    targetTrackIndex = targetTrack.originalIndex === -1 ? 0 : targetTrack.originalIndex;
    isNewTrack = targetTrack.originalIndex === -1 || targetTrackIndex >= allTracks.length;
  }
  
  // 确定吸附区域
  let snapZone: 'top' | 'center' | 'bottom' = 'center';
  if (insertPosition === 'above') {
    snapZone = 'top';
  } else if (insertPosition === 'below') {
    snapZone = 'bottom';
  }

  return { 
    targetTrackIndex, 
    isValidDrop: true, 
    isNewTrack,
    insertPosition,
    snapZone
  };
}

// =======================================================================
// Timeline Operation Functions - 时间轴操作函数
// =======================================================================

/**
 * @function deepCopyTimeline
 * @description 深拷贝时间轴数据，确保 Vue 响应性更新时不污染原始数据
 */
export function deepCopyTimeline(timeline: Timeline): Timeline {
  return JSON.parse(JSON.stringify(timeline));
}

/**
 * @function getTrackClips
 * @description 根据轨道类型获取对应的片段数组
 */
export function getTrackClips(timeline: Timeline, type: TrackType, trackIndex: number = 0): TrackClipsResult {
  let trackClips: TimelineClip[] | undefined;
  let found = false;

  switch (type) {
    case 'video':
      trackClips = timeline.VideoTracks?.[trackIndex]?.VideoTrackClips;
      found = !!(timeline.VideoTracks?.[trackIndex]?.VideoTrackClips);
      break;
    case 'audio':
      trackClips = timeline.AudioTracks?.[trackIndex]?.AudioTrackClips;
      found = !!(timeline.AudioTracks?.[trackIndex]?.AudioTrackClips);
      break;
    case 'subtitle':
      trackClips = timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips;
      found = !!(timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips);
      break;
    default:
      console.warn(`不支持的轨道类型: ${type}`);
      return { trackClips: undefined, found: false };
  }

  return { trackClips, found };
}

/**
 * @function validateClipIndex
 * @description 验证片段索引是否在有效范围内
 */
export function validateClipIndex(trackClips: TimelineClip[] | undefined, index: number, operation: string): boolean {
  if (!trackClips || trackClips.length <= index) {
    console.warn(`${operation}失败：索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`);
    return false;
  }
  return true;
}

/**
 * @function updateClipTimeRange
 * @description 更新片段的时间范围，保持原始持续时间
 */
export function updateClipTimeRange(clip: TimelineClip, newStartTime: number): TimelineClip {
  // 计算原始片段的持续时间
  const originalDuration = clip.TimelineOut - clip.TimelineIn;

  // 更新 TimelineIn 和 TimelineOut
  clip.TimelineIn = newStartTime;
  clip.TimelineOut = newStartTime + originalDuration;

  return clip;
}

/**
 * @function validateCutTime
 * @description 验证切割时间点是否在片段的有效范围内
 */
export function validateCutTime(clip: TimelineClip, cutTime: number): boolean {
  if (cutTime <= clip.TimelineIn || cutTime >= clip.TimelineOut) {
    console.warn(
      `切割点 ${cutTime.toFixed(2)} 秒不在片段有效范围 (${clip.TimelineIn.toFixed(2)} - ${clip.TimelineOut.toFixed(2)} 秒)`
    );
    return false;
  }
  return true;
}

/**
 * @function createCutClips
 * @description 根据切割时间点创建两个新片段
 */
export function createCutClips(originalClip: TimelineClip, cutTime: number): [TimelineClip, TimelineClip] {
  const cutPointInMedia = (cutTime - originalClip.TimelineIn) + originalClip.In;
  const newClip1: TimelineClip = {
    ...originalClip,
    TimelineOut: cutTime,
    Out: cutPointInMedia
  };

  const newClip2: TimelineClip = {
    ...originalClip,
    TimelineIn: cutTime,
    In: cutPointInMedia
  };

  return [newClip1, newClip2];
}

/**
 * @function cutClipAtTime
 * @description 在指定时间点切割时间轴中的片段
 */
export function cutClipAtTime(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number,
  cutTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '切割')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 获取要切割的片段
  const clipToCut = trackClips![clipIndex];

  // 5. 验证切割时间点
  if (!validateCutTime(clipToCut, cutTime)) {
    const error = `切割时间点 ${cutTime} 不在有效范围内`;
    return { success: false, error };
  }

  // 6. 创建切割后的两个片段
  const [newClip1, newClip2] = createCutClips(clipToCut, cutTime);

  // 7. 替换原片段
  trackClips!.splice(clipIndex, 1, newClip1, newClip2);

  return { success: true, timeline: newTimeline };
}

/**
 * @function deleteClipAtIndex
 * @description 删除指定索引的片段
 */
export function deleteClipAtIndex(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '删除')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 删除片段
  trackClips!.splice(clipIndex, 1);

  return { success: true, timeline: newTimeline };
}

/**
 * @function updateClipTimeAtIndex
 * @description 更新时间轴中指定片段的时间范围
 */
export function updateClipTimeAtIndex(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number,
  clipIndex: number,
  newStartTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
  if (!found) {
    const error = `未找到 ${type} 轨道 ${trackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, clipIndex, '时间更新')) {
    const error = `索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 更新片段时间范围
  const clipToUpdate = trackClips![clipIndex];
  updateClipTimeRange(clipToUpdate, newStartTime);

  return { success: true, timeline: newTimeline };
}

/**
 * @function ensureTrackExists
 * @description 确保指定索引的轨道存在，如果不存在则创建
 */
export function ensureTrackExists(
  timeline: Timeline,
  type: TrackType,
  trackIndex: number
): TimelineOperationResult {
  switch (type) {
    case 'video':
      // 确保VideoTracks数组存在
      if (!timeline.VideoTracks) {
        timeline.VideoTracks = [];
      }
      // 扩展数组到目标索引
      while (timeline.VideoTracks.length <= trackIndex) {
        timeline.VideoTracks.push({
          VideoTrackClips: []
        });
      }
      break;
    
    case 'audio':
      if (!timeline.AudioTracks) {
        timeline.AudioTracks = [];
      }
      while (timeline.AudioTracks.length <= trackIndex) {
        timeline.AudioTracks.push({
          AudioTrackClips: []
        });
      }
      break;
    
    case 'subtitle':
      if (!timeline.SubtitleTracks) {
        timeline.SubtitleTracks = [];
      }
      while (timeline.SubtitleTracks.length <= trackIndex) {
        timeline.SubtitleTracks.push({
          SubtitleTrackClips: []
        });
      }
      break;
    
    default:
      const error = `不支持的轨道类型: ${type}`;
      return { success: false, error };
  }

  return { success: true, timeline };
}

/**
 * @function moveClipToTrack
 * @description 将片段移动到不同轨道，支持同类型轨道间的跨轨道移动
 */
export function moveClipToTrack(
  timeline: Timeline,
  type: TrackType,
  sourceTrackIndex: number,
  clipIndex: number,
  targetTrackIndex: number,
  newStartTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);

  // 2. 获取源轨道的片段数组
  const { trackClips: sourceTrackClips, found: sourceFound } = getTrackClips(newTimeline, type, sourceTrackIndex);
  if (!sourceFound) {
    const error = `未找到源 ${type} 轨道 ${sourceTrackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 3. 验证片段索引
  if (!validateClipIndex(sourceTrackClips, clipIndex, '移动')) {
    const error = `片段索引 ${clipIndex} 超出范围（总共 ${sourceTrackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }

  // 4. 获取要移动的片段
  const clipToMove = sourceTrackClips![clipIndex];
  
  // 5. 确保目标轨道存在
  const targetTrackExists = ensureTrackExists(newTimeline, type, targetTrackIndex);
  if (!targetTrackExists.success) {
    return { success: false, error: targetTrackExists.error };
  }

  // 6. 获取目标轨道
  const { trackClips: targetTrackClips, found: targetFound } = getTrackClips(newTimeline, type, targetTrackIndex);
  if (!targetFound) {
    const error = `未找到目标 ${type} 轨道 ${targetTrackIndex}`;
    console.warn(error);
    return { success: false, error };
  }

  // 7. 从源轨道移除片段
  sourceTrackClips!.splice(clipIndex, 1);

  // 8. 更新片段的时间位置
  const updatedClip = updateClipTimeRange({ ...clipToMove }, newStartTime);

  // 9. 将片段添加到目标轨道
  targetTrackClips!.push(updatedClip);

  // 10. 按时间排序目标轨道的片段
  targetTrackClips!.sort((a, b) => a.TimelineIn - b.TimelineIn);

  console.log(`✅ 片段移动成功: 从 ${type} 轨道 ${sourceTrackIndex} 移动到轨道 ${targetTrackIndex}`);
  return { success: true, timeline: newTimeline };
}

/**
 * @function normalizeTimeline
 * @description 归一化时间轴数据，确保字幕片段正确分类
 */
export function normalizeTimeline(timeline: Timeline): Timeline {
  // 简化的归一化实现，主要确保数据结构完整
  const normalized = deepCopyTimeline(timeline);
  
  // 确保基本的轨道数组存在
  if (!normalized.VideoTracks) normalized.VideoTracks = [];
  if (!normalized.AudioTracks) normalized.AudioTracks = [];
  if (!normalized.SubtitleTracks) normalized.SubtitleTracks = [];
  
  return normalized;
}
