<template>
  <div class="app-container">
    <div class="task-card animate__animated animate__fadeIn">
      <div class="task-header">
        <span class="task-title">创建视频合成任务</span>
        <span class="task-title version-tag">M版</span>
      </div>

      <div class="task-content">
        <!-- 左侧区域 -->
        <transition name="slide-fade">
          <div class="content-section left-section">
            <!-- 音频上传区域 -->
            <div class="section-title">提供音频素材</div>
            <div class="uploadbox" v-loading="audioloading">
              <!-- 隐藏的audio元素，用于播放控制 -->
              <audio ref="audioElement" :src="uploadMP" v-if="uploadMP" @loadedmetadata="onLoadedMetadata" @play="onPlay" @pause="onPause" @timeupdate="updateProgress" @ended="onEnded"></audio>
              <div v-if="uploadMP" class="audio-player">
                <div class="player-controls">
                  <button :class="isPlaying ? 'pause-btn' : 'play-btn'" @click="isPlaying ? pauseAudio() : playAudio()">
                    <el-icon><component :is="isPlaying ? 'video-pause' : 'video-play'" /></el-icon>
                  </button>
                  <div class="audio-info">
                    <div class="progress-container">
                      <div class="progress-bar" @click="seekAudio">
                        <div class="progress-bar-fill" :style="{ width: progress + '%' }"></div>
                        <div class="progress-handle" :style="{ left: progress + '%' }"></div>
                      </div>
                      <div class="time-info">
                        <span class="current-time">{{ formatTime(currentTime) }}</span>
                        <span class="duration">{{ formatTime(duration) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <el-button link type="danger" @click="clearAudio" class="clear-audio"><el-icon><delete /></el-icon></el-button>
              </div>
              <div v-else class="upload-section">
                <el-upload class="upload-demo" drag :headers="upload.headers" :before-upload="handleBeforeUpload1" 
                  :on-change="handleUploadChange1" :action="upload.url" accept="audio/*" ref="uploadRef1" 
                  :on-progress="handleFileUploadProgress1" :on-success="handleFileSuccess1">
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">将音频拖到此处 或 <em>点击上传</em></div>
                </el-upload>
                <div class="select-audio-section">
                  <el-button type="primary" link class="select-material-btn" @click="goToAudioPage">
                    <el-icon><folder-opened /></el-icon>
                    选择音频素材
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 视频预览区域 -->
            <div class="section-header">
              <div class="section-title mt20">形象素材预览</div>
              <el-button v-if="uploadvideo" link type="danger" @click="clearVideo" class="clear-video-btn animate__animated animate__fadeIn">
                <el-icon><delete /></el-icon>删除预览视频
              </el-button>
            </div>
            
            <div class="video-container" v-loading="imageLoading" element-loading-text="正在加载中...">
              <video v-if="uploadvideo" controls class="preview-video" :src="uploadvideo"></video>
              <div v-else class="empty-video">
                <el-icon><video-camera /></el-icon>
                <span>您还没有提供形象素材</span>
                <el-button type="primary" size="small" @click="goToImagePage">选择形象</el-button>
              </div>
            </div>

            <!-- 控制区域 -->
            <div class="control-section">
              <el-tooltip content="请选择-7到7之间的调整值" placement="top" effect="light" :show-after="500">
                <div class="adjust-input-wrapper">
                  <el-input v-model.number="bboxShiftvalue" type="number" placeholder="请选择调整值 (-7~7)"
                    class="adjust-input" :min="-7" :max="7" @input="validateInput" @blur="handleInputBlur"
                    :disabled="finishloading">
                    <template #prefix>
                      <el-icon><edit /></el-icon>
                    </template>
                    <template #suffix>
                      <span class="input-limit-text">调整值: [-7, 7]</span>
                    </template>
                  </el-input>
                  <div class="input-slider">
                    <el-slider v-model="bboxShiftvalue" :min="-7" :max="7" :step="1" show-stops
                      :disabled="finishloading"/>
                  </div>
                </div>
              </el-tooltip>
              <el-button type="primary" class="create-btn" @click="handlecreate" :loading="finishloading" 
                :disabled="finishloading || !canCreateVideo">
                <el-icon><video-play /></el-icon>开始合成
              </el-button>
              <el-button class="reset-btn" @click="resetForm" :disabled="finishloading">
                <el-icon><refresh /></el-icon>重置
              </el-button>
            </div>
          </div>
        </transition>

        <!-- 右侧结果区域 -->
        <transition name="slide-fade">
          <div class="content-section right-section" v-loading="finishloading">
            <div class="section-title">合成结果</div>
            <div class="video-container result-container">
              <video v-if="finshedvideo" :src="finshedvideo" controls class="result-video animate__animated animate__fadeIn"></video>
              <div v-else class="empty-result animate__animated animate__fadeIn">
                <el-icon><video-camera /></el-icon><span>合成结果将在这里显示</span>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { ElMessage } from 'element-plus';
import { createMusetalk, genStatus } from "@/api/platform/video";
import { imageDetail } from "@/api/platform/image"; 
import { audioDetail } from '@/api/platform/audio';
import { onMounted, onBeforeUnmount, watch, nextTick, ref, reactive, computed } from "vue";
import { useRoute, useRouter } from 'vue-router';

// 基础状态
const route = useRoute();
const router = useRouter();
const uploadMP = ref("");
const uploadvideo = ref("");
const getMP = ref("");
const drivenAudioMd5 = ref(""); // 只存md5
const drivenVideoMd5 = ref("");
const finshedvideo = ref("");
const bboxShiftvalue = ref(0);
const finishloading = ref(false);
const audioloading = ref(false);
const audioElement = ref(null);
const isPlaying = ref(false);
const progress = ref(0);
const currentTime = ref(0);
const duration = ref(0);
const imageLoading = ref(false); // 新增：形象加载状态
const realAudioPath = ref(""); // 只用于提交任务

// 上传配置
const upload = reactive({
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/platform/video/upload"
});

let actualImagePath = null;
let timer = null;

// 音频控制
function playAudio() {
  if (!audioElement.value || !duration.value) return;
  audioElement.value.currentTime = currentTime.value;
  audioElement.value.play();
  isPlaying.value = true;
  audioloading.value = false;
}

function pauseAudio() {
  if (!audioElement.value) return;
  audioElement.value.pause();
  isPlaying.value = false;
}

function clearAudio() {
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value.currentTime = 0;
  }
  uploadMP.value = "";
  isPlaying.value = false;
  progress.value = 0;
  currentTime.value = 0;
  duration.value = 0; // 只在完全清除时重置duration
  getMP.value = "";
  drivenAudioMd5.value = "";
  realAudioPath.value = ""; // 清除真实音频路径
}

function updateProgress() {
  if (!audioElement.value) return;
  currentTime.value = audioElement.value.currentTime;
  duration.value = audioElement.value.duration;
  progress.value = (currentTime.value / duration.value) * 100;
}

// 添加新的音频控制函数
function seekAudio(e) {
  if (!audioElement.value) return;
  const progressBar = e.currentTarget;
  const rect = progressBar.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const percentage = x / progressBar.offsetWidth;
  const time = percentage * duration.value;
  audioElement.value.currentTime = time;
  progress.value = percentage * 100;
}

// 添加音频加载完成的处理函数
const onLoadedMetadata = () => {
  if (audioElement.value) {
    duration.value = audioElement.value.duration;
    currentTime.value = 0;
    progress.value = 0;
  }
};

// 视频控制
function clearVideo() {
  uploadvideo.value = "";
  actualImagePath = null;
  drivenVideoMd5.value = null;
}

// 处理从音频页面返回的音频素材
const handleAudioSelect = async (audioPath) => {
  if (!audioPath) return;
  
  try {
    audioloading.value = true;
    const response = await audioDetail(audioPath);
    console.log(response,'md5===sadjksadjska');
    
    
    if (response.code === 200 && response.data) {
      // 用于播放器播放
      uploadMP.value = response.data.audioUrl;
      // 用于提交任务/存数据库（真实地址，不带token）
      realAudioPath.value = response.data.audioPath;
      drivenAudioMd5.value = response.data.audioMd5 || response.data.md5 || "";
      console.log(drivenAudioMd5.value);
      
      
      // 等待下一个tick确保DOM更新后再预加载音频信息
      await nextTick();
      
      // 预加载音频时长等信息
      if (audioElement.value) {
        // 如果audio元素已存在，直接更新src并监听loadedmetadata事件
        audioElement.value.src = response.data.audioUrl;
        audioElement.value.load(); // 重新加载音频
      } else {
        // 如果audio元素不存在，创建临时audio对象获取时长信息
        const audio = new Audio();
        audio.src = response.data.audioUrl;
        await new Promise((resolve, reject) => {
          audio.addEventListener('loadedmetadata', () => {
            duration.value = audio.duration;
            currentTime.value = 0;
            progress.value = 0;
            resolve();
          });
          audio.addEventListener('error', () => reject(new Error('音频加载失败')));
        });
      }
    }
  } catch (error) {
    console.error('获取音频详情失败:', error);
    ElMessage.error('获取音频详情失败');
  } finally {
    audioloading.value = false;
  }
};

// 视频初始化，不影响音频状态
async function initVideo(imageAddress) {
  if (!imageAddress) return;
  
  try {
    imageLoading.value = true;
    const response = await imageDetail(imageAddress);
    if (response.code === 200 && response.data) {
      uploadvideo.value = response.data.url;
      actualImagePath = response.data.imageAddress;
      drivenVideoMd5.value = response.data.md5;
    }
  } catch (error) {
    console.error('加载形象视频失败:', error);
  } finally {
    imageLoading.value = false;
  }
}

// 工具函数
function formatTime(time) {
  if (!time) return '00:00';
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function validateInput(value) {
  const numValue = Number(value);
  if (isNaN(numValue) || value === '' || value === null) {
    bboxShiftvalue.value = 0;
    return;
  }
  bboxShiftvalue.value = Math.max(-7, Math.min(7, numValue));
}

// 文件上传处理
function handleBeforeUpload1(file) {
  // 定义允许的音频格式
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg', 'audio/m4a'];
  const maxSize = 100 * 1024 * 1024; // 100MB

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 MP3/WAV/OGG/M4A 格式的音频文件！');
    return false;
  }

  // 检查文件大小
  if (file.size > maxSize) {
    ElMessage.error('音频文件大小不能超过 100MB！');
    return false;
  }
  return true;
}

async function handleUploadChange1(file) {
  // 验证文件类型
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg', 'audio/m4a'];
  if (file.raw && allowedTypes.includes(file.raw.type)) {
    audioloading.value = true;
    uploadMP.value = URL.createObjectURL(file.raw);
    try {
      const audio = new Audio();
      audio.src = uploadMP.value;
      await new Promise((resolve, reject) => {
        audio.addEventListener('loadedmetadata', () => {
          duration.value = audio.duration;
          currentTime.value = 0;
          progress.value = 0;
          audioloading.value = false;
          resolve();
        });
        audio.addEventListener('error', () => { reject(new Error('音频文件加载失败'));});
      });
    } catch (error) {
      ElMessage.error('音频文件加载失败，请检查文件是否损坏');
      clearAudio();
    }
  } else {
    if (file.raw) {
      URL.revokeObjectURL(uploadMP.value);
    }
    uploadMP.value = '';
  }
}

function handleFileSuccess1(response) {
  nextTick(() => {
    getMP.value = response.url; // 预览用url
    realAudioPath.value = response.audioPath || response.url; // 优先用真实路径
    drivenAudioMd5.value = response.audioMd5 || response.md5 || ""; // 优先audioMd5
    isPlaying.value = false;
    audioloading.value = false;
  });
}

// 检查是否可以创建视频
const canCreateVideo = computed(() => {
  return !!realAudioPath.value && !!uploadvideo.value && !!actualImagePath;
});

// 合成处理
async function handlecreate() {
  if (!realAudioPath.value) {
    ElMessage.error('请提供音频素材！');
    return;
  }
  if (!uploadvideo.value) {
    ElMessage.error('请选择形象素材！');
    return;
  }
  if (!actualImagePath) {
    ElMessage.error('未能找到对应的形象素材实际路径！');
    return;
  }
  
  // 设置加载状态，禁用按钮
  finishloading.value = true;
  
  try {
    const res = await createMusetalk({
      drivenAudio: realAudioPath.value, // 真实文件地址
      drivenVideo: actualImagePath,
      bboxShiftValue: bboxShiftvalue.value,
      drivenVideoMd5: drivenVideoMd5.value,
      drivenAudioMd5: drivenAudioMd5.value // md5，字段名与后端保持一致
    });
    if (res.code === 200) {
      ElMessage.success('任务已创建，正在处理中...');
      startPolling(res.data.id);
    } else {
      // 请求成功但业务失败，恢复按钮状态
      ElMessage.error(res.msg || '创建任务失败');
      finishloading.value = false;
    }
  } catch (error) {
    ElMessage.error('创建任务失败');
    finishloading.value = false;  // 出错时恢复按钮状态
  }
}

function startPolling(taskId) {
  timer = setInterval(async () => {
    try {
      const res = await genStatus(taskId);
      if (res.data.status === '3') {
        finshedvideo.value = res.data.resultVideo;
        stopPolling();
        finishloading.value = false;  // 成功时恢复按钮状态
        ElMessage.success('视频合成成功');
      }
      if (res.data.status === '4') {
        stopPolling();
        finishloading.value = false;  // 失败时恢复按钮状态
        ElMessage.error('生成失败');
      }
    } catch (error) {
      stopPolling();
      finishloading.value = false;  // 出错时恢复按钮状态
      ElMessage.error('检查任务状态失败');
    }
  }, 3000);
}

function stopPolling() {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
}

// 生命周期处理
onMounted(() => {
  // 初始化加载，处理所有可能的参数来源
  const { audioPath, imageAddress, keepAudioPath, keepImageAddress } = route.query;
  // 优先使用保留的参数，然后使用直接参数
  if (keepAudioPath || audioPath) {
    handleAudioSelect(keepAudioPath || audioPath);
  }
  if (keepImageAddress || imageAddress) {
    initVideo(keepImageAddress || imageAddress);
  }
});

// 监听路由参数变化, 只在对应参数变化时更新相应内容
watch(
  () => route.query,
  (newQuery, oldQuery) => {
    // 处理从音频页面返回的情况
    if (newQuery.from === 'textAudio' && newQuery.audioPath) {
      handleAudioSelect(newQuery.audioPath);
      // 如果有保留的形象地址且当前没有加载形象，则恢复形象
      if (newQuery.keepImageAddress && !uploadvideo.value) {
        initVideo(newQuery.keepImageAddress);
      }
    }
    
    // 处理从形象页面返回的情况
    if (newQuery.from === 'image' && newQuery.imageAddress) {
      initVideo(newQuery.imageAddress);
      // 如果有保留的音频路径且当前没有加载音频，则恢复音频
      if (newQuery.keepAudioPath && !uploadMP.value) {
        handleAudioSelect(newQuery.keepAudioPath);
      }
    }
    
    // 处理普通的参数变化（非页面返回的情况）
    if (!newQuery.from) {
      if (newQuery.audioPath && newQuery.audioPath !== oldQuery?.audioPath && newQuery.audioPath !== drivenAudioMd5.value) {
        handleAudioSelect(newQuery.audioPath);
      }
      if (newQuery.imageAddress && newQuery.imageAddress !== oldQuery?.imageAddress && newQuery.imageAddress !== actualImagePath) {
        initVideo(newQuery.imageAddress);
      }
    }
  },
  { deep: true }
);

// 只在页面销毁时停止轮询
onBeforeUnmount(() => {
  stopPolling();
});

// 事件处理
const onPlay = () => isPlaying.value = true;
const onPause = () => isPlaying.value = false;
const onEnded = () => {
  isPlaying.value = false;
  progress.value = 0;
  currentTime.value = 0;
  if (audioElement.value) {
    audioElement.value.currentTime = 0;
  }
};

// 添加输入框blur事件处理函数
function handleInputBlur() {
  validateInput(bboxShiftvalue.value);
}

// 跳转到形象素材页面
function goToImagePage() {
  // 保留当前已选择的音频路径（使用实际的音频路径而不是MD5）
  const currentAudioPath = drivenAudioMd5.value || route.query.audioPath;
  router.push({ 
    path: "/szbVideo/image",
    query: {
      from: 'createMusetalk',
      version: 'M', // M版标识
      returnPath: '/szbVideo/createMusetalk',
      // 传递当前的音频路径，确保在形象页面选择后返回时能带回
      ...(currentAudioPath && { keepAudioPath: currentAudioPath })
    }
  });
}

// 跳转到音频素材页面
function goToAudioPage() {
  // 保留当前已选择的形象地址
  const currentImageAddress = actualImagePath || route.query.imageAddress;
  router.push({
    path: '/szbSound/textAudio',
    query: {
      from: 'musetalk',
      versionKey: 'm', // 当前为M版
      ...(currentImageAddress && { keepImageAddress: currentImageAddress })
    }
  });
}

// 重置表单
function resetForm() {
  // 清除音频
  clearAudio();
  // 清除视频
  clearVideo();
  // 清除结果视频
  finshedvideo.value = "";
  // 重置调整值
  bboxShiftvalue.value = 0;
  // 停止轮询
  stopPolling();
  // 重置加载状态
  finishloading.value = false;
  audioloading.value = false;
  imageLoading.value = false;
}
</script>

<style lang="scss" scoped>
@keyframes commonAnim { from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); } }

.task-card {
  background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); overflow: hidden; margin-bottom: 20px;
  transition: all 0.3s ease;
  &:hover { transform: translateY(-2px); box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); }
}

.task-header {
  padding: 20px; 
  background: linear-gradient(to right, #409eff, #66b1ff); 
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between; // 左右对齐
  align-items: center;  
  .version-tag {
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: normal;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .task-title { 
    font-size: 20px; font-weight: 600; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);       
  }
}

.task-content {
  display: grid; grid-template-columns: 1fr 1fr; gap: 24px; padding: 24px; min-height: 600px;
}

.content-section {
  display: flex;
  flex-direction: column;

  .section-title {
    font-size: 18px; font-weight: 500; color: #333; margin-bottom: 16px; padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .section-title {
      margin-bottom: 0;
      border-bottom: none;
    }
  }

  &.right-section {
    // 调整右侧区域，使内容区域有同样的布局结构
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .video-container {
      flex-grow: 1;
      margin-bottom: 24px;
      height: 640px;
    }

    // 为了创建一个与左侧控制区域占用相同高度的空白区域
    &::after {
      content: '';
      display: block;
      height: 80px; // 大致模拟左侧控制区域高度
    }
  }
}

// 统一视频容器样式
.video-container {
  height: 400px; background: #f8f9fa; border-radius: 8px; overflow: hidden;
  transition: all 0.3s ease; border: 2px solid transparent;
  &:hover { border-color: #409eff; box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1); }

  video { width: 100%; height: 100%; object-fit: contain; background: #000; }

  .empty-video, .empty-result {
    height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;
    .el-icon { font-size: 48px; margin-bottom: 16px; color: #409eff; }
    span { font-size: 16px; color: #606266; margin-bottom: 12px; }
  }
}

// 统一控制区域样式
.control-section {
  margin-top: 24px; display: flex; gap: 16px; align-items: center;

  .adjust-input-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .adjust-input {
      :deep(.el-input__wrapper) {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff;
        }
      }

      :deep(.el-input__inner) {
        text-align: center;
        font-weight: 500;
      }

      .input-limit-text {
        color: #909399;
        font-size: 12px;
      }
    }

    .input-slider {
      padding: 0 10px;
      
      :deep(.el-slider) {
        .el-slider__runway {
          margin: 8px 0;
        }
        
        .el-slider__stop {
          background-color: #409eff;
        }

        .el-slider__button {
          border-color: #409eff;
          transition: transform 0.3s ease;
          
          &:hover {
            transform: scale(1.2);
          }
        }
      }
    }
  }

  .create-btn {
    min-width: 140px; height: 40px; display: flex; align-items: center; justify-content: center; gap: 8px;
    transition: all 0.3s ease;
    &:hover:not(:disabled) { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3); }
    
    &:disabled {
      cursor: not-allowed;
      background: #a0cfff;
      border-color: #a0cfff;
      color: white;
      opacity: 0.8;
    }
  }

  .reset-btn {
    min-width: 120px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-radius: 8px;
    font-weight: 500;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    color: #606266;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) { 
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      background: #ecf5ff;
      border-color: #409eff;
      color: #409eff;
    }
    
    &:disabled {
      cursor: not-allowed;
      background: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      opacity: 0.8;
    }

    .el-icon {
      font-size: 16px;
    }
  }
}

// 音频播放器样式
.audio-player {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .player-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }

  .audio-info {
    flex: 1;
    min-width: 0; // 防止内容溢出
  }

  .progress-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .progress-bar {
    height: 4px;
    background: #e4e7ed;
    border-radius: 2px;
    cursor: pointer;
    position: relative;

    &:hover {
      .progress-handle {
        transform: translate(-50%, -50%) scale(1);
      }
      .progress-bar-fill {
        background: #66b1ff;
      }
    }
  }

  .progress-bar-fill {
    height: 100%;
    background: #409eff;
    border-radius: 2px;
    transition: width 0.1s linear, background-color 0.3s ease;
  }

  .progress-handle {
    width: 12px;
    height: 12px;
    background: #fff;
    border: 2px solid #409eff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
    z-index: 1;
  }

  .time-info {
    display: flex;
    justify-content: space-between;
    color: #606266;
    font-size: 12px;
    
    .current-time, .duration {
      min-width: 45px;
      text-align: center;
    }
  }

  .play-btn,
  .pause-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    background-color: #409eff;

    &:hover {
      transform: scale(1.1);
      background-color: #66b1ff;
    }
    
    .el-icon {
      font-size: 20px;
    }
  }

  .clear-audio {
    padding: 8px;
    border-radius: 50%;
    
    &:hover {
      background-color: rgba(245, 108, 108, 0.1);
    }
  }
}

// 上传区域样式
.uploadbox {
  margin-bottom: 20px;
  .upload-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .select-audio-section {
    margin-top: 10px;
    text-align: center;
  }
}

// 动画相关
.slide-fade-enter-active, .slide-fade-leave-active { transition: all 0.5s ease; }
.slide-fade-enter-from, .slide-fade-leave-to { opacity: 0; transform: translateX(20px); }

// 加载效果
:deep(.el-loading-mask) { background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(4px); }
:deep(.el-loading-spinner .circular) { animation: rotate 2s linear infinite; }

.mt20 {
  margin-top: 20px;
}
</style>
