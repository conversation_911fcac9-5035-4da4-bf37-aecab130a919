import request from '@/utils/request';
import type {
  MediaInfoQuery,
  MediaInfoResponse,
  MediaPayload,
  MediaListQueryParams,
  MediaSearchParams,
  MediaListResponse,
  GetEditingProjectMaterialsRequest,
  GetEditingProjectMaterialsResponse,
  MediaUploadRequest,
  MediaUploadResponse,
  MediaRegisterRequest,
  MediaRegisterResponse,
  UploadAndRegisterResponse,
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse
} from '../types/media';

interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 获取单个媒资的详细信息
 * <p>
 * 通过调用阿里云ICE(智能媒体服务)的 GetMediaInfo 接口，获取媒资详情。
 * 必须提供 MediaId 或 InputURL 两者之一。
 * </p>
 *
 * @param params GetMediaInfo的请求参数
 * @returns 返回一个包含了媒资详细信息的Promise。
 */
export function getMediaInfo(params: MediaInfoQuery): Promise<MediaInfoResponse> {
  // Pass the generic to get a typed response wrapper, then unwrap the 'data' property.
  return request<MediaInfoResponse>({
    url: '/video/media/info',
    method: 'get',
    params
  }).then(res => res.data);
}
/**
 * 获取剪辑工程关联素材
 * <p>
 * 通过调用阿里云ICE的 GetEditingProjectMaterials 接口，获取当前工程绑定的所有素材。
 * </p>
 *
 * @param projectId 云剪辑工程ID（必填）
 * @returns 返回一个包含工程关联素材信息的Promis
 */
export function getEditingProjectMaterials(projectId: string): Promise<GetEditingProjectMaterialsResponse> {
  return request<GetEditingProjectMaterialsResponse>({
    url: `/video/media/project/${projectId}/materials`,
    method: 'get',
  }).then(res => res.data);
}

/**
 * 增加剪辑关联素材
 * <p>
 * 通过调用阿里云ICE的 addEditingProjectMaterials 接口，增加剪辑工程的关联素材。
 * </p>
 * 
 * @param projectId 云剪辑工程ID（必填）
 * @param materialIds 素材ID列表（必填）
 * @returns 返回一个包含操作结果的Promise
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function addEditingProjectMaterials(
  projectId: string,
  materialIds: string[]
): Promise<ApiResponse<void>> {
  return request<ApiResponse<void>>({
    url: `/video/media/addProjectMaterials`,
    method: 'post',
    data: { projectId, materialIds }
  }).then(res => res.data);
}


/**
 * 一体化媒资上传与注册接口
 * @param file 要上传的文件
 * @param category 媒资分类 (video/audio/music/image/font)
 * @param metadata 可选的媒资元数据
 * @returns 返回包含媒资ID等信息的Promise
 */
export function uploadAndRegisterMedia(
  file: File,
  category: string,
  metadata?: {
    title?: string;
    description?: string;
    tags?: string;
    businessType?: string;
  }
): Promise<UploadAndRegisterResponse> {
  // 处理文件名，移除特殊字符避免OSS路径问题
  const sanitizeFileName = (fileName: string): string => {
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    
    // 替换特殊字符为安全字符
    const safeName = nameWithoutExt
      .replace(/[^\w\u4e00-\u9fa5.-]/g, '_') // 只保留字母、数字、中文、点、横线
      .replace(/_{2,}/g, '_') // 多个下划线合并为一个
      .replace(/^_+|_+$/g, ''); // 移除开头结尾的下划线
    
    return safeName + extension;
  };

  // 创建新的File对象，使用安全的文件名
  const safeFileName = sanitizeFileName(file.name);
  const safeFile = new File([file], safeFileName, { type: file.type });

  const formData = new FormData();
  formData.append('file', safeFile);
  formData.append('category', category);
  
  // 添加可选元数据
  if (metadata) {
    Object.entries(metadata).forEach(([key, value]) => {
      if (value) {
        formData.append(key, value);
      }
    });
  }
  
  return request<UploadAndRegisterResponse>({
    url: '/video/media/uploadAndRegister',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      isToken: true
    }
  }).then(res => {
    return res.data;
  }).catch(error => {
    console.error('上传失败:', {
      fileName: safeFileName,
      category: category,
      error: error.response?.data?.msg || error.message
    });
    throw error;
  });
}

/**
 * 批量获取媒资信息
 * <p>
 * 通过调用阿里云ICE的 BatchGetMediaInfos 接口，批量获取媒资详情。支持传入多个 MediaId。
 * </p>
 * 
 * @param params 请求参数
 * @returns 返回批量媒资信息
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function batchGetMediaInfos(
  params: BatchGetMediaInfosRequest
): Promise<BatchGetMediaInfosResponse> {
  return request<BatchGetMediaInfosResponse>({
    url: '/video/media/batchGetMediaInfos',
    method: 'get',
    data: params
  }).then(res => res.data);
}

/**
 * 批量获取媒资信息（简化版本）
 * 
 * @param mediaIds 媒资ID数组
 * @param additionType 额外信息类型
 * @returns 返回批量媒资信息
 */
export function batchGetMediaInfosSimple(
  mediaIds: string[],
  additionType?: string[] | ""
): Promise<BatchGetMediaInfosResponse> {
  return request<BatchGetMediaInfosResponse>({
    url: '/video/media/batchGetMediaInfos',
    method: 'get',
    params: {
      mediaIds: mediaIds.join(','),
      additionType: additionType ? additionType.join(',') : undefined
    }
  }).then(res => res.data);
}


/**
 * 列出媒资基础信息列表
 * <p>
 * 通过调用阿里云ICE的 listMediaBasicInfo 接口，获取媒资列表。
 * 支持按类型、状态、标签等条件筛选。
 * </p>
 * 
 * @param params 查询参数
 * @returns 返回媒资列表
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function listMediaBasicInfo(params: MediaListQueryParams): Promise<MediaListResponse> {
  return request<MediaListResponse>({
    url: '/video/media/listMediaBasicInfo',
    method: 'get',
    params
  }).then(res => res.data);
}

/**
 * 搜索媒资
 * <p>
 * 通过关键词搜索媒资，支持标题、描述、标签等字段的模糊搜索。
 * </p>
 * 
 * @param params 搜索参数
 * @returns 返回搜索结果
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function searchMediaInfos(params: MediaSearchParams): Promise<MediaListResponse> {
  return request<{
    RequestId: string;
    MediaInfos: any[];
    TotalCount: number;
    PageNumber: number;
    PageSize: number;
  }>({
    url: '/video/media/search',
    method: 'get',
    params
  }).then(res => res.data);
}

// 导出类型定义，方便其他模块使用
export type {
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse
};

// 重新导出工具类，保持向后兼容
export { BatchMediaInfoUtils, MediaInfoCache, MediaInfoAnalyzer } from '../utils/batchMediaUtils';

// ============ 以下为已废弃的旧接口，保留用于兼容性 ============

/**
 * @deprecated 已废弃，请使用 uploadAndRegisterMedia 一体化接口
 * 获取OSS上传授权
 */
export function createUploadMedia(params: { fileInfo: { name: string; ext: string; type: string; size?: number } }): Promise<{ signedUrl: string; uploadId: string }> {
  return request<{ signedUrl: string; uploadId: string }>({
    url: '/video/media/createUploadMedia',
    method: 'post',
    data: params
  }).then(res => res.data);
}