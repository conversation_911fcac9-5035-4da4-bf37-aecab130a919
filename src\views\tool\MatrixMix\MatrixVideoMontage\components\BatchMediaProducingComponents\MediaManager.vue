<template>
  <div class="media-manager">
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-folder"></i>
          <span>媒体素材管理</span>
        </div>
      </template>
      
      <!-- 媒体组列表 -->
      <div class="media-groups">
        <div 
          v-for="(group, index) in groups" 
          :key="index"
          class="media-group"
        >
          <div class="group-header">
            <el-input 
              v-model="group.name"
              placeholder="请输入组名"
              size="small"
              style="width: 200px;"
              @input="handleGroupsChange"
            />
            <div class="group-actions">
              <el-button 
                type="primary" 
                size="small" 
                icon="el-icon-plus"
                @click="openMediaSelector(index)"
              >
                添加素材
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                icon="el-icon-delete"
                @click="removeGroup(index)"
                :disabled="groups.length <= 1"
              >
                删除组
              </el-button>
            </div>
          </div>
          
          <!-- 媒体文件列表 -->
          <div class="media-list">
            <div 
              v-for="(media, mediaIndex) in group.media" 
              :key="media.id"
              class="media-item"
            >
              <img 
                :src="media.thumbnail" 
                :alt="media.name"
                class="media-thumbnail"
                @error="handleImageError"
              />
              <div class="media-info">
                <div class="media-name">{{ media.name }}</div>
                <div class="media-meta">
                  <span class="media-type">{{ getMediaTypeText(media.type) }}</span>
                  <span class="media-duration" v-if="media.duration > 0">
                    {{ formatDuration(media.duration) }}
                  </span>
                  <span class="media-size">{{ media.size }}</span>
                </div>
              </div>
              <el-button 
                type="danger" 
                size="small" 
                icon="el-icon-delete"
                @click="removeMedia(index, mediaIndex)"
              />
            </div>
            
            <div v-if="group.media.length === 0" class="empty-media">
              <i class="el-icon-picture"></i>
              <p>暂无素材，点击"添加素材"按钮添加</p>
            </div>
          </div>
        </div>
        
        <el-button 
          type="primary" 
          icon="el-icon-plus"
          @click="addGroup"
          class="add-group-btn"
        >
          添加媒体组
        </el-button>
      </div>
    </el-card>

    <!-- 媒体选择弹窗 -->
    <MediaSelectorDialog
      :visible="mediaSelectorVisible"
      @update:visible="mediaSelectorVisible = $event"
      @confirm="handleMediaSelected"
      @close="closeMediaSelector"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, defineProps, defineEmits } from 'vue';
import type { MediaItem, MediaGroupUI } from '../../../types/batchProducing';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';

// Props定义
interface Props {
  modelValue: MediaGroupUI[];
}

// Emits定义
interface Emits {
  (e: 'update:modelValue', value: MediaGroupUI[]): void;
  (e: 'api-config-changed', value: any[]): void;
  (e: 'media-added', value: { groupIndex: number; media: MediaItem[] }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const groups = ref<MediaGroupUI[]>(JSON.parse(JSON.stringify(props.modelValue)));
const mediaSelectorVisible = ref(false);
const currentGroupIndex = ref(0);
const isOpening = ref(false);

// 媒体选择器相关 - 这些变量现在由MediaSelectorDialog内部管理

// 防止循环更新的标志
const isUpdatingFromProps = ref(false);

// 监听props变化
watch(
  () => props.modelValue,
  (val) => {
    if (!isUpdatingFromProps.value) {
      isUpdatingFromProps.value = true;
      groups.value = JSON.parse(JSON.stringify(val));
      nextTick(() => {
        isUpdatingFromProps.value = false;
      });
    }
  },
  { deep: true }
);

// 监听本地数据变化
watch(
  groups,
  (val) => {
    if (!isUpdatingFromProps.value) {
      const uiGroups = JSON.parse(JSON.stringify(val));
      emit('update:modelValue', uiGroups);

      // 计算API配置
      const apiConfig = val
        .filter(group => group.media.length > 0)
        .map(group => ({
          GroupName: group.name,
          MediaArray: group.media.map(media => media.id),
          SplitMode: 'NoSplit' as const,
          Volume: 1.0
        }));
      emit('api-config-changed', apiConfig);
    }
  },
  { deep: true }
);

// 组管理方法
const handleGroupsChange = () => {
  // 触发响应式更新
};

const addGroup = () => {
  groups.value.push({
    name: `媒体组${groups.value.length + 1}`,
    media: []
  });
};

const removeGroup = (index: number) => {
  groups.value.splice(index, 1);
};

// 媒体管理方法
const removeMedia = (groupIndex: number, mediaIndex: number) => {
  groups.value[groupIndex].media.splice(mediaIndex, 1);
};

// 媒体选择器方法
const openMediaSelector = (groupIndex: number) => {
  if (isOpening.value || mediaSelectorVisible.value) {
    return;
  }

  isOpening.value = true;
  currentGroupIndex.value = groupIndex;
  mediaSelectorVisible.value = true;

  setTimeout(() => {
    isOpening.value = false;
  }, 300);
};

// 处理媒体选择确认
const handleMediaSelected = (selectedMedia: MediaItem[]) => {
  if (currentGroupIndex.value !== -1) {
    groups.value[currentGroupIndex.value].media.push(...selectedMedia);
    emit('media-added', {
      groupIndex: currentGroupIndex.value,
      media: selectedMedia
    });
  }
  closeMediaSelector();
};

// 关闭媒体选择器
const closeMediaSelector = () => {
  mediaSelectorVisible.value = false;
  currentGroupIndex.value = -1;
};

// 工具方法
const getMediaTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    video: '视频',
    image: '图片',
    audio: '音频'
  };
  return typeMap[type] || type;
};

const formatDuration = (duration: number) => {
  if (!duration) return '';
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};



const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = '/default-thumbnail.png';
};
</script>

<style scoped>
.media-manager {
  width: 100%;
  padding: 8px 0 0 0;
}

.config-card {
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0 8px 8px 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #222;
  font-size: 15px;
  padding: 2px 0;
}

.media-groups {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.media-group {
  border: 1px solid #e4e7ed;
  border-radius: 7px;
  padding: 10px 12px 10px 12px;
  background: #fcfcfc;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.group-actions {
  display: flex;
  gap: 6px;
}

.media-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 48px;
}

.media-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 5px;
  background: #fff;
  min-width: 160px;
  transition: box-shadow 0.2s, border-color 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
}
.media-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}

.media-thumbnail {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.media-info {
  flex: 1;
  min-width: 0;
}

.media-name {
  font-size: 13px;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-meta {
  font-size: 11px;
  color: #909399;
  display: flex;
  gap: 6px;
}

.empty-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
  color: #bfbfbf;
  width: 100%;
  font-size: 13px;
}

.empty-media i {
  font-size: 32px;
  margin-bottom: 6px;
}

.add-group-btn {
  align-self: flex-start;
  margin-top: 4px;
  font-size: 13px;
  padding: 4px 14px;
  border-radius: 5px;
}

/* 媒体选择器样式 */
.media-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-bar {
  display: flex;
  gap: 12px;
  align-items: center;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
  min-height: 300px;
}

.media-grid-item {
  position: relative;
  border: 2px solid #e4e7ed;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.media-grid-item:hover {
  border-color: #409eff;
}

.media-grid-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.grid-thumbnail {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
}

.grid-info {
  margin-top: 8px;
}

.grid-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.select-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  color: #606266;
  font-size: 14px;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  padding: 8px 12px;
}

/* 优化el-input尺寸 */
:deep(.el-input--small .el-input__inner) {
  height: 28px;
  font-size: 13px;
  border-radius: 4px;
}

/* 优化el-button尺寸 */
:deep(.el-button--small) {
  padding: 4px 10px;
  font-size: 13px;
  border-radius: 4px;
}

/* 优化el-icon尺寸 */
:deep(.el-icon) {
  font-size: 16px;
}

</style>
