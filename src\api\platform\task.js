import request from '@/utils/request'

// 查询任务管理列表
export function listTask(query) {
  return request({
    url: '/platform/task/list',
    method: 'get',
    params: query
  })
}

// 查询任务管理详细
export function getTask(taskId) {
  return request({
    url: '/platform/task/' + taskId,
    method: 'get'
  })
}

// 创建训练声音任务
export function createTrainTask(query) {
  return request({
    url: '/platform/task/train/audio',
    method: 'post',
    params: query
  })
}

// 新增任务管理
export function addTask(data) {
  return request({
    url: '/platform/task',
    method: 'post',
    data: data
  })
}

// 修改任务管理
export function updateTask(data) {
  return request({
    url: '/platform/task',
    method: 'put',
    data: data
  })
}

// 删除任务管理
export function delTask(taskId) {
  return request({
    url: '/platform/task/' + taskId,
    method: 'delete'
  })
}

//创建一个声音推理任务
export function createTask(model_name,platformArticles) {
  return request({
    url: '/platform/task/tts/audio',
    method: 'post',
    params:{
      model_name:model_name
    },
    data:platformArticles
  })
}

// 创建文本转音频任务
export function createTextToAudio(params) {
  return request({
    url: '/platform/task/text/audio',
    method: 'post',
    params: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
