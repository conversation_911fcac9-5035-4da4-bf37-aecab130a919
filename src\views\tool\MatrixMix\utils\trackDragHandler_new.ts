/**
 * @file trackDragHandler.ts
 * @description 专业的轨道拖拽处理器，实现类似阿里云智能媒体生产的轨道拖拽体验
 *              支持精确的轨道定位、智能插入、吸附对齐等功能
 */

import { 
  smartTrackDragCalculation, 
  addPreviewTrackToDisplayTracks,
  type TrackType,
  type DragCalculationResult 
} from './timelineUtils';

/**
 * @interface DragState
 * @description 拖拽状态接口
 */
export interface DragState {
  isDragging: boolean;
  draggedClip?: any;
  draggedTrackType?: TrackType;
  sourceTrackIndex?: number;
  dragStartY?: number;
  dragStartTime?: number;
  previewTrackIndex?: number | null;
  currentDragResult?: DragCalculationResult;
  isDraggingFromLibrary?: boolean;
}

/**
 * @interface VisualFeedback
 * @description 视觉反馈接口
 */
export interface VisualFeedback {
  showInsertLine: boolean;
  insertLinePosition?: number;
  highlightTrackIndex?: number;
  insertLineStyle?: 'solid' | 'dashed' | 'dotted';
  cursorStyle?: 'grab' | 'grabbing' | 'copy' | 'not-allowed';
}

/**
 * @interface DragUpdateResult
 * @description 拖拽更新结果接口
 */
export interface DragUpdateResult {
  dragResult: DragCalculationResult;
  visualFeedback: VisualFeedback;
  updatedDisplayTracks: any[];
}

/**
 * @class TrackDragHandler
 * @description 轨道拖拽处理器类 - 简化版本，主要用于状态管理和计算
 */
export class TrackDragHandler {
  private dragState: DragState = {
    isDragging: false
  };
  
  private visualFeedback: VisualFeedback = {
    showInsertLine: false
  };

  private trackHeight: number;
  private snapThreshold: number;

  constructor(trackHeight: number = 60, snapThreshold: number = 15) {
    this.trackHeight = trackHeight;
    this.snapThreshold = snapThreshold;
  }

  /**
   * @method startDrag
   * @description 开始拖拽
   */
  startDrag(params: {
    clip: any;
    trackType: TrackType;
    sourceTrackIndex: number;
    startY: number;
    isDraggingFromLibrary?: boolean;
  }): void {
    const { clip, trackType, sourceTrackIndex, startY, isDraggingFromLibrary = false } = params;
    
    this.dragState = {
      isDragging: true,
      draggedClip: clip,
      draggedTrackType: trackType,
      sourceTrackIndex,
      dragStartY: startY,
      dragStartTime: Date.now(),
      isDraggingFromLibrary
    };

    console.log('🎯 开始拖拽:', {
      trackType,
      sourceTrackIndex,
      isDraggingFromLibrary
    });
  }

  /**
   * @method updateDrag
   * @description 更新拖拽位置
   */
  updateDrag(params: {
    mouseY: number;
    scrollAreaRect: DOMRect;
    scrollTop: number;
    displayTracks: any[];
    allTracks: any[];
  }): DragUpdateResult {
    if (!this.dragState.isDragging) {
      throw new Error('拖拽未开始');
    }

    const { mouseY, scrollAreaRect, scrollTop, displayTracks, allTracks } = params;

    // 使用智能拖拽计算
    const dragResult = smartTrackDragCalculation({
      mouseY,
      trackType: this.dragState.draggedTrackType!,
      scrollAreaRect,
      scrollTop,
      trackHeight: this.trackHeight,
      displayTracks,
      allTracks,
      sourceTrackIndex: this.dragState.sourceTrackIndex,
      dragStartY: this.dragState.dragStartY,
      isDraggingFromLibrary: this.dragState.isDraggingFromLibrary
    });

    this.dragState.currentDragResult = dragResult;

    // 生成视觉反馈
    const visualFeedback = this.generateVisualFeedback(dragResult);
    this.visualFeedback = visualFeedback;

    // 更新显示轨道（添加预览轨道）
    const updatedDisplayTracks = addPreviewTrackToDisplayTracks(
      displayTracks,
      this.dragState.draggedTrackType!,
      dragResult,
      true
    );

    return {
      dragResult,
      visualFeedback,
      updatedDisplayTracks
    };
  }

  /**
   * @method endDrag
   * @description 结束拖拽，返回拖拽结果供调用方处理
   */
  endDrag(): { 
    success: boolean; 
    dragResult?: DragCalculationResult; 
    dragState?: DragState;
  } {
    if (!this.dragState.isDragging || !this.dragState.currentDragResult) {
      return { success: false };
    }

    const result = {
      success: true,
      dragResult: this.dragState.currentDragResult,
      dragState: { ...this.dragState }
    };

    // 重置状态
    this.reset();
    
    return result;
  }

  /**
   * @method generateVisualFeedback
   * @description 生成视觉反馈
   */
  private generateVisualFeedback(dragResult: DragCalculationResult): VisualFeedback {
    if (!dragResult.isValidDrop) {
      return {
        showInsertLine: false,
        cursorStyle: 'not-allowed'
      };
    }

    if (dragResult.isNewTrack) {
      return {
        showInsertLine: true,
        insertLinePosition: dragResult.targetTrackIndex || 0,
        insertLineStyle: 'solid',
        cursorStyle: 'copy'
      };
    }

    return {
      showInsertLine: false,
      highlightTrackIndex: dragResult.targetTrackIndex || undefined,
      cursorStyle: 'grabbing'
    };
  }

  /**
   * @method reset
   * @description 重置拖拽状态
   */
  reset(): void {
    this.dragState = {
      isDragging: false
    };
    
    this.visualFeedback = {
      showInsertLine: false
    };
  }

  /**
   * @method getDragState
   * @description 获取当前拖拽状态
   */
  getDragState(): Readonly<DragState> {
    return { ...this.dragState };
  }

  /**
   * @method getVisualFeedback  
   * @description 获取当前视觉反馈
   */
  getVisualFeedback(): Readonly<VisualFeedback> {
    return { ...this.visualFeedback };
  }

  /**
   * @method isDragging
   * @description 检查是否正在拖拽
   */
  isDragging(): boolean {
    return this.dragState.isDragging;
  }

  /**
   * @method getCurrentDragResult
   * @description 获取当前拖拽计算结果
   */
  getCurrentDragResult(): DragCalculationResult | undefined {
    return this.dragState.currentDragResult;
  }
}

/**
 * @function createTrackDragHandler
 * @description 创建轨道拖拽处理器实例的工厂函数
 */
export function createTrackDragHandler(trackHeight: number = 60, snapThreshold: number = 15): TrackDragHandler {
  return new TrackDragHandler(trackHeight, snapThreshold);
}

/**
 * @interface DragManager
 * @description 拖拽管理器接口，提供更高级的拖拽管理功能
 */
export interface DragManager {
  handler: TrackDragHandler;
  startDrag: TrackDragHandler['startDrag'];
  updateDrag: TrackDragHandler['updateDrag'];  
  endDrag: TrackDragHandler['endDrag'];
  reset: TrackDragHandler['reset'];
  getDragState: TrackDragHandler['getDragState'];
  getVisualFeedback: TrackDragHandler['getVisualFeedback'];
  getCurrentDragResult: TrackDragHandler['getCurrentDragResult'];
}

/**
 * @function createDragManager
 * @description 创建拖拽管理器实例
 */
export function createDragManager(trackHeight: number = 60, snapThreshold: number = 15): DragManager {
  const handler = createTrackDragHandler(trackHeight, snapThreshold);
  
  return {
    handler,
    startDrag: handler.startDrag.bind(handler),
    updateDrag: handler.updateDrag.bind(handler),
    endDrag: handler.endDrag.bind(handler),
    reset: handler.reset.bind(handler),
    getDragState: handler.getDragState.bind(handler),
    getVisualFeedback: handler.getVisualFeedback.bind(handler),
    getCurrentDragResult: handler.getCurrentDragResult.bind(handler)
  };
}
