<template>
  <div class="sound-and-text-app-container">
    <div class="two-column-layout">
      <el-card class="text-audio-container">
        <div class="content-area">
          <div class="form-title">文本转音频</div>
          <el-form :model="form" :rules="rules" ref="formRef" class="audio-form">
            <!-- 文本输入 -->
            <div class="form-section">
              <div class="section-label">
                <el-icon><EditPen /></el-icon>
                <span>输入文本</span>
              </div>
              <el-form-item prop="text">
                <el-input
                  v-model="form.text"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入要转换为音频的文本内容..."
                  maxlength="1000"
                  show-word-limit
                  resize="none"
                  class="text-input"
                />
              </el-form-item>
            </div>

            <!-- 声音选择按钮式布局，带滚动条 -->
            <div class="form-section">
              <div class="section-label">
                <el-icon><Microphone /></el-icon>
                <span>选择声音</span>
              </div>
              <el-form-item prop="modelName">
                <el-scrollbar class="sound-btn-scrollbar">
                  <div class="sound-btn-list">
                    <button
                      v-for="(sound, idx) in soundList"
                      :key="sound.value"
                      class="sound-btn"
                      :class="{ selected: form.modelName === sound.value }"
                      :style="{ background: sound.color }"
                      @click.prevent="form.modelName = sound.value"
                    >
                      <span class="sound-btn-label">{{ sound.label }}</span>
                    </button>
                  </div>
                </el-scrollbar>
              </el-form-item>
            </div>

            <div class="form-actions">
              <el-button
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                :disabled="!canSubmit"
                class="submit-btn"
              >
                {{ loading ? '转换中...' : '开始转换' }}
              </el-button>
            </div>
          </el-form>
        </div>
      </el-card>
      <!-- 音频列表 -->
      <el-card class="audio-list-container">
        <AudioList style="width: 100%;" :version-key="versionKey"/>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { EditPen, Microphone } from '@element-plus/icons-vue'
import { createTextToAudio } from '@/api/platform/task'
import AudioList from './components/AudioList.vue'
import { listSound } from '@/api/platform/sound'
import { useRoute } from 'vue-router'

const route = useRoute()
const versionKey = route.query.versionKey || null

const formRef = ref()
const loading = ref(false)
const form = reactive({
  text: '',
  modelName: ''
})

const canSubmit = computed(() => {
  return form.text.trim() && form.modelName && !loading.value
})

const rules = {
  text: [
    { required: true, message: '请输入文本内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '文本长度应在1-1000个字符之间', trigger: 'blur' }
  ],
  modelName: [
    { required: true, message: '请选择声音模型', trigger: 'change' }
  ]
}

// element-plus主题色
const elColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#626aef', '#1abc9c', '#e67e22', '#e84393', '#00b894'
]

// 声音列表（动态获取）
const soundList = ref([])

const fetchSounds = async () => {
  try {
    const res = await listSound({ soundStatus: '2' })
    if (res.total > 0) {
      soundList.value = res.rows.map((item, idx) => ({
        label: item.soundName,
        value: item.soundId,
        color: elColors[idx % elColors.length],
        // 可扩展更多字段如图片等
      }))
    } else {
      soundList.value = []
    }
  } catch (error) {
    soundList.value = []
    console.error('获取声音列表失败:', error)
  }
}

onMounted(() => {
  fetchSounds()
})

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    const params = {
      text: form.text,
      model_name: form.modelName,
      category_id: '',
    }
    await createTextToAudio(params)
    ElMessage.success('文本转音频创建成功！')
    // 清空表单
    form.text = ''
    form.modelName = ''
    formRef.value.resetFields()
  } catch (error) {
    console.error('创建文本转音频任务失败:', error)
    ElMessage.error('创建任务失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.sound-and-text-app-container {
  height: 720px;
  width: 100%;
}

.two-column-layout {
  display: flex;
  gap: 32px;
  max-width: 1500px;
  margin-left: 15px;
  align-items: flex-start;
  flex-wrap: wrap;
  @media (max-width: 1100px) {
    flex-direction: column;
    gap: 24px;
  }
}

.text-audio-container {
  
  flex: 1 1 420px;
  max-width: 480px;
  min-width: 320px;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  border: none;
  background: #fff;
  padding: 0;
  .content-area {
    height: 720px;
    padding: 32px 28px 24px 28px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  .form-title {
    font-size: 22px;
    font-weight: 700;
    color: #3b3f5c;
    margin-bottom: 28px;
    letter-spacing: 1px;
    text-align: center;
  }
  .audio-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
  }
  .form-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px 18px 12px 18px;
    box-shadow: 0 1px 4px rgba(102, 126, 234, 0.04);
    border: 1px solid #e8eaed;
    transition: all 0.3s ease;
    margin-bottom: 8px;
    &:hover {
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
      border-color: #667eea;
    }
  }
  .section-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    .el-icon {
      color: #667eea;
      font-size: 18px;
    }
  }
  .sound-btn-scrollbar {
    max-height: 140px;
    min-height: 56px;
    padding-bottom: 2px;
    margin: 0 2px;
    --el-scrollbar-bg-color: #f8fafc;
    --el-scrollbar-thumb-bg-color: #dcdfe6;
    --el-scrollbar-thumb-hover-bg-color: #b3b3b3;
  }
  .sound-btn-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    width: 100%;
    padding: 4px 0 4px 0;
    box-sizing: border-box;
    justify-items: center;
    align-items: center;
    @media (max-width: 600px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
  }
  .sound-btn {
    width: 103px;
    min-width: 0;
    height: 48px;
    border-radius: 10px;
    border: 2px solid transparent;
    color: #fff;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    outline: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0;
    &:hover, &.selected {
      border-color: #409EFF;
      color: #409EFF;
      background: #fff !important;
    }
    .sound-btn-label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 90%;
      text-align: center;
    }
  }
  .text-input :deep(.el-textarea__inner) {
    border: 2px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    transition: all 0.3s ease;
    background: #fafbfc;
    &:focus {
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
  .submit-btn {
    padding: 12px 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 200px;
    &:hover:not(.is-disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }
    &:active:not(.is-disabled) {
      transform: translateY(0);
    }
    &.is-loading {
      background: #a0aec0;
    }
    &.is-disabled {
      background: #e2e8f0;
      color: #a0aec0;
      cursor: not-allowed;
    }
  }
}

.audio-list-container {
  flex: 2 1 600px;
  min-width: 340px;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  border: none;
  background: #fff;
  padding: 0 0 8px 0;
  display: flex;
  flex-direction: column;
  .audio-list-scroll {
    flex: 1;
    overflow-y: auto;
    padding: 0 28px 0 28px;
  }
}

@media (max-width: 900px) {
  .two-column-layout {
    flex-direction: column;
    gap: 18px;
  }
  .text-audio-container, .audio-list-container {
    max-width: 100%;
    min-width: 0;
  }
  .audio-list-container {
    padding: 0;
  }
}


</style>