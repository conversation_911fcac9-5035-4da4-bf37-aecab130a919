<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="词表表名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入词表表名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['tingwu:phrase:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['tingwu:phrase:remove']">批量删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="phraseList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="词表表名" align="center" prop="name" />
            <el-table-column label="词表描述" align="center" prop="description" />
            <el-table-column label="词表权重" align="center" prop="wordWeights" width="300">
                <template #default="scope">
                    <div v-if="scope.row.wordWeights" class="word-weights-display">
                        <div class="tags-container">
                            <el-tag v-for="(item, index) in getDisplayWordWeights(scope.row.wordWeights)" :key="index"
                                size="small" class="weight-tag" :type="getTagType(item.weight)">
                                {{ item.word }}:{{ item.weight }}
                            </el-tag>
                            <el-button
                                v-if="shouldShowMoreButton(scope.row.wordWeights)"
                                type="primary"
                                link
                                size="small"
                                @click="showWordWeightsDetail(scope.row)"
                                class="more-button">
                                +{{ getExtraCount(scope.row.wordWeights) }}更多
                            </el-button>
                        </div>
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['tingwu:phrase:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['tingwu:phrase:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改词表信息对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="phraseRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="词表表名" prop="name">
                    <el-input v-model="form.name" placeholder="请输入词表表名" />
                </el-form-item>
                <el-form-item label="词表描述" prop="description">
                    <el-input v-model="form.description" placeholder="请输入词表描述" />
                </el-form-item>
                <el-form-item prop="wordWeights">
                    <template #label>
                        <div class="form-label-with-tip">
                            <span class="label-text">词表权重</span>
                            <div class="help-icon-wrapper">
                                <el-tooltip content="权重 > 0：增大识别概率，常用值为2；权重 = -6：尽量不识别该词语；权重过大可能影响其他词语识别准确性"
                                    placement="top" effect="dark">
                                    <el-icon class="help-icon">
                                        <QuestionFilled />
                                    </el-icon>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                    <div class="word-weights-container">
                        <div v-for="(item, index) in wordWeightsList" :key="index" class="word-weight-item">
                            <div class="input-row">
                                <el-input v-model="item.word" placeholder="请输入词汇" style="width: 45%"
                                    @input="handleWordInput(item, $event)" @blur="validateWord(item)"
                                    :class="{ 'input-error': item.error }" />
                                <el-input-number v-model="item.weight" :min="-6" :max="5" :step="1" placeholder="权重"
                                    style="width: 35%; margin: 0 10px" @change="updateWordWeights" />
                                <el-button type="danger" icon="Delete" size="small" circle
                                    @click="removeWordWeight(index)" :disabled="isDeleteDisabled()" />
                            </div>
                            <div v-if="item.error" class="error-message">{{ item.error }}</div>
                        </div>
                        <el-button type="primary" icon="Plus" size="small" @click="addWordWeight"
                            style="margin-top: 10px">
                            添加词汇
                        </el-button>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 词表权重详情弹窗 -->
        <WordWeightDetail
            v-model:visible="weightDetailOpen"
            :phrase-detail="currentPhraseDetail"
            :word-weights="currentWordWeights"
        />
    </div>
</template>

<script setup name="Phrase">
import { listPhrase, getPhraseByphraseId, delPhraseByPhraseIds, addPhrase, updatePhrase } from "@/api/tingwu/phrase"
import { QuestionFilled } from '@element-plus/icons-vue'
import WordWeightDetail from './components/WordWeightDetail.vue'

const { proxy } = getCurrentInstance()

const phraseList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const wordWeightsList = ref([{ word: '', weight: 0, error: null }])

// 词表权重详情弹窗相关
const weightDetailOpen = ref(false)
const currentPhraseDetail = ref({})
const currentWordWeights = ref([])

// 显示配置
const MAX_DISPLAY_TAGS = 3 // 最多显示的标签数量

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        description: null,
        wordWeights: null
    },
    rules: {
        name: [
            { required: true, message: "词表表名不能为空", trigger: "blur" }
        ],
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询词表信息列表 */
function getList() {
    loading.value = true
    listPhrase(queryParams.value).then(response => {
        phraseList.value = response.rows
        total.value = response.total
        loading.value = false
    })
}

// 取消按钮
function cancel() {
    open.value = false
    reset()
}

// 表单重置
function reset() {
    form.value = {
        keyId: null,
        name: null,
        description: null,
        wordWeights: null
    }
    wordWeightsList.value = [{ word: '', weight: 0, error: null }]
    proxy.resetForm("phraseRef")
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.phraseId)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
    reset()
    open.value = true
    title.value = "添加词表信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset()
    const _phraseId = row.phraseId || ids.value
    console.log('这里的参数是什么>>>', _phraseId)
    getPhraseByphraseId(_phraseId).then(response => {
        console.log('查询结果>>>', response)
        const data = response.data[0]
        console.log('查询结果>>>', form.value)

        form.value = {
            phraseId: data.PhraseId,
            name: data.Name,
            description: data.Description,
            wordWeights: data.WordWeights
        }
        // 处理wordWeights - 现在它已经是对象了，不需要JSON.parse
        console.log('wordWeights 类型:', typeof form.value.wordWeights, '值:', form.value.wordWeights)
        if (form.value.wordWeights) {
            try {
                let weightsObj = form.value.wordWeights

                // 如果是字符串，才需要解析
                if (typeof weightsObj === 'string') {
                    console.log('wordWeights 是字符串，需要解析')
                    weightsObj = JSON.parse(weightsObj)
                } else {
                    console.log('wordWeights 已经是对象，直接使用')
                }

                // 转换为数组格式
                wordWeightsList.value = Object.entries(weightsObj).map(([word, weight]) => ({
                    word,
                    weight: Number(weight),
                    error: null
                }))
                console.log('转换后的 wordWeightsList:', wordWeightsList.value)
            } catch (e) {
                console.error('解析词表权重失败:', e)
                console.error('原始数据:', form.value.wordWeights)
                wordWeightsList.value = [{ word: '', weight: 0, error: null }]
            }
        } else {
            wordWeightsList.value = [{ word: '', weight: 0, error: null }]
        }
        open.value = true
        title.value = "修改词表信息"
    })
}

/** 提交按钮 */
function submitForm() {
    // 验证所有词汇
    let hasWordError = false
    wordWeightsList.value.forEach(item => {
        if (item.word && item.word.trim()) {
            validateWord(item)
            if (item.error) {
                hasWordError = true
            }
        }
    })

    if (hasWordError) {
        proxy.$modal.msgError("请修正词汇输入错误后再提交")
        return
    }

    proxy.$refs["phraseRef"].validate(valid => {
        if (valid) {
            if (form.value.phraseId != null) {
                updatePhrase(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功")
                    open.value = false
                    getList()
                })
            } else {
                addPhrase(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功")
                    open.value = false
                    getList()
                })
            }
        }
    })
}

/** 删除按钮操作 */
function handleDelete(row) {
    console.log("handleDelete", row)
    const _phraseIds = row.phraseId || ids.value
    proxy.$modal.confirm('是否确认删除所选词表的数据项？').then(function () {
        return delPhraseByPhraseIds(_phraseIds)
    }).then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
    }).catch(() => { })
}



/** 添加词汇权重项 */
function addWordWeight() {
    wordWeightsList.value.push({ word: '', weight: 0, error: null })
}

/** 删除词汇权重项 */
function removeWordWeight(index) {
    // 如果只有一个项且为空，则不删除
    if (wordWeightsList.value.length === 1) {
        const firstItem = wordWeightsList.value[0]
        if (!firstItem.word || firstItem.word.trim() === '') {
            return
        }
    }

    // 删除指定项
    wordWeightsList.value.splice(index, 1)

    // 如果删除后列表为空，添加一个空项
    if (wordWeightsList.value.length === 0) {
        wordWeightsList.value.push({ word: '', weight: 0, error: null })
    }

    updateWordWeights()
}

/** 更新词汇权重JSON字符串 */
function updateWordWeights() {
    const weightsObj = {}
    let hasValidWords = false

    wordWeightsList.value.forEach(item => {
        if (item.word && item.word.trim() && !item.error) {
            weightsObj[item.word.trim()] = item.weight !== undefined ? item.weight : 0
            hasValidWords = true
        }
    })

    form.value.wordWeights = hasValidWords ? JSON.stringify(weightsObj) : null
}

/** 解析词汇权重为显示数组 */
function parseWordWeights(wordWeights) {
    try {
        let weightsObj = wordWeights

        // 如果是字符串，才需要解析
        if (typeof weightsObj === 'string') {
            weightsObj = JSON.parse(weightsObj)
        }

        // 如果已经是对象，直接使用
        return Object.entries(weightsObj).map(([word, weight]) => ({
            word,
            weight
        }))
    } catch (e) {
        return []
    }
}

/** 获取用于表格显示的词汇权重（限制数量） */
function getDisplayWordWeights(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return allWeights.slice(0, MAX_DISPLAY_TAGS)
}

/** 判断是否需要显示"更多"按钮 */
function shouldShowMoreButton(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return allWeights.length > MAX_DISPLAY_TAGS
}

/** 获取超出显示数量的词汇个数 */
function getExtraCount(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return Math.max(0, allWeights.length - MAX_DISPLAY_TAGS)
}

/** 根据权重值获取标签类型 */
function getTagType(weight) {
    if (weight > 0) {
        return 'success' // 绿色 - 增大识别概率
    } else if (weight === -6) {
        return 'danger' // 红色 - 尽量不识别
    } else if (weight < 0) {
        return 'warning' // 橙色 - 负权重
    } else {
        return '' // 默认颜色 - 权重为0
    }
}

/** 显示词表权重详情 */
function showWordWeightsDetail(row) {
    currentPhraseDetail.value = {
        name: row.name,
        description: row.description
    }
    currentWordWeights.value = parseWordWeights(row.wordWeights)
    weightDetailOpen.value = true
}





/** 验证词汇格式 */
function validateWordFormat(word) {
    // 检查长度
    if (word.length > 10) {
        return '词汇长度不能超过10个字符'
    }

    // 检查是否包含数字
    const numberRegex = /[0-9]/
    if (numberRegex.test(word)) {
        return '词汇不能包含数字，如"58.9元"需要替换为"五十八点九元"'
    }

    // 检查是否包含标点符号和特殊字符
    const punctuationRegex = /[^\u4e00-\u9fa5a-zA-Z]/
    if (punctuationRegex.test(word)) {
        return '词汇不能包含标点符号和特殊字符'
    }

    return null
}

/** 处理词汇输入 */
function handleWordInput(item, value) {
    // 直接更新item的word值
    item.word = value
    // 清除之前的错误信息
    item.error = null

    updateWordWeights()
}

/** 验证词汇 */
function validateWord(item) {
    if (item.word && item.word.trim()) {
        const error = validateWordFormat(item.word.trim())
        item.error = error
    } else {
        item.error = null
    }
}

/** 判断删除按钮是否应该禁用 */
function isDeleteDisabled() {
    // 如果只有一个词汇项且该项为空，则禁用删除按钮
    if (wordWeightsList.value.length === 1) {
        const firstItem = wordWeightsList.value[0]
        return !firstItem.word || firstItem.word.trim() === ''
    }
    // 如果有多个词汇项，则允许删除
    return false
}

getList()
</script>

<style scoped>
.word-weights-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 12px;
    background-color: #fafafa;
}

.word-weight-item {
    margin-bottom: 20px;
}

.word-weight-item:last-of-type {
    margin-bottom: 0;
}

.input-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.input-error :deep(.el-input__wrapper) {
    border-color: #f56c6c;
    box-shadow: 0 0 0 1px #f56c6c inset;
}

.error-message {
    color: #f56c6c;
    font-size: 12px;
    margin-left: 0;
    margin-top: 2px;
}

.form-label-with-tip {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    white-space: nowrap;
    min-width: fit-content;
}

.label-text {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;

}

.help-icon-wrapper {
    display: flex;
    justify-content: flex-start;
}

.help-icon {
    font-size: 14px;
    color: #a8abb2;
    cursor: help;
    transition: color 0.3s ease;
    border-radius: 50%;
    padding: 2px;
}

.help-icon:hover {
    color: #409eff;
    background-color: #f0f9ff;
}

/* 词表权重显示样式 */
.word-weights-display {
    max-width: 100%;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
    justify-content: center;
}

.weight-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 12px;
    padding: 2px 8px;
    white-space: nowrap;
}

.more-button {
    font-size: 12px;
    padding: 2px 6px;
    margin: 0;
    height: auto;
    line-height: 1.2;
}


</style>
